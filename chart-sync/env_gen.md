

## DEVTRON Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | APP | string |chart-sync |  |  | false |
 | APP_STORE_APPLICATION_VERSIONS_SAVE_CHUNK_SIZE | int |20 |  |  | false |
 | APP_SYNC_SHUTDOWN_WAIT_DURATION | int |120 |  |  | false |
 | CHART_PROVIDER_ID | string |* |  |  | false |
 | IS_OCI_REGISTRY | bool |true |  |  | false |
 | K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST | int |25 |  |  | false |
 | K8s_TCP_IDLE_CONN_TIMEOUT | int |300 |  |  | false |
 | K8s_TCP_KEEPALIVE | int |30 |  |  | false |
 | K8s_TCP_TIMEOUT | int |30 |  |  | false |
 | K8s_TLS_HANDSHAKE_TIMEOUT | int |10 |  |  | false |
 | LOG_LEVEL | int |0 |  |  | false |
 | NO_PROXY | string |registry.hub.docker.com,*.docker.com,*.docker.io,docker.io,registry-1.docker.io,github.com,ghcr.io,pkg-containers.githubusercontent.com |  |  | false |
 | PARALLELISM_LIMIT_FOR_TAG_PROCESSING | int |0 |  |  | false |
 | PG_ADDR | string |127.0.0.1 |  |  | false |
 | PG_DATABASE | string |orchestrator |  |  | false |
 | PG_EXPORT_PROM_METRICS | bool |true |  |  | false |
 | PG_LOG_ALL_FAILURE_QUERIES | bool |true |  |  | false |
 | PG_LOG_ALL_QUERY | bool |false |  |  | false |
 | PG_LOG_SLOW_QUERY | bool |true |  |  | false |
 | PG_PASSWORD | string |password |  |  | false |
 | PG_PORT | string |5432 |  |  | false |
 | PG_QUERY_DUR_THRESHOLD | int64 |5000 |  |  | false |
 | PG_USER | string |user |  |  | false |
 | PROMETHEUS_MATRIX_PORT | int |8080 |  |  | false |
 | RUNTIME_CONFIG_LOCAL_DEV | LocalDevMode |false |  |  | false |
 | USE_CUSTOM_HTTP_TRANSPORT | bool |false |  |  | false |

