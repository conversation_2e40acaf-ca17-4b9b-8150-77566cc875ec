[{"Category": "DEVTRON", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "chart-sync", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "APP_STORE_APPLICATION_VERSIONS_SAVE_CHUNK_SIZE", "EnvType": "int", "EnvValue": "20", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "APP_SYNC_SHUTDOWN_WAIT_DURATION", "EnvType": "int", "EnvValue": "120", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CHART_PROVIDER_ID", "EnvType": "string", "EnvValue": "*", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "IS_OCI_REGISTRY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "NO_PROXY", "EnvType": "string", "EnvValue": "registry.hub.docker.com,*.docker.com,*.docker.io,docker.io,registry-1.docker.io,github.com,ghcr.io,pkg-containers.githubusercontent.com", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PARALLELISM_LIMIT_FOR_TAG_PROCESSING", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "password", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "user", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PROMETHEUS_MATRIX_PORT", "EnvType": "int", "EnvValue": "8080", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}]