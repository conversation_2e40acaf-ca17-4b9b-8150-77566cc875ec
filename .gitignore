# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/
.DS_Store
.idea
.vscode
.env
/cmd/external-app/devtron-ea
#binary
casbin/casbin-enterprise
chart-sync/chart-sync
ci-runner/cirunner
ci-runner/ci-runner-enterprise
git-sensor/git-sensor
kubelink/kubelink
scoop/main
image-scanner/image-scanner
kubewatch/kubewatch
license-manager/license-manager