[{"Category": "DEVTRON", "Fields": [{"Env": "AWS_REGION", "EnvType": "string", "EnvValue": "", "EnvDescription": "aws region", "Example": "ap-south-1", "Deprecated": "false"}, {"Env": "AWS_SECRET_MANAGER_CONFIG", "EnvType": "string", "EnvValue": "", "EnvDescription": "aws secret manager config to get private key and shared secret", "Example": "{'accessKey'':'xxx','passkey':'xxx','region':'xxx'}", "Deprecated": "false"}, {"Env": "AWS_SECRET_NAME", "EnvType": "string", "EnvValue": "", "EnvDescription": "aws secret name to get private key and shared secret", "Example": "devtron-license-manager-secret", "Deprecated": "false"}, {"Env": "DEVTRON_DEFAULT_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_DEX_SECRET_NAMESPACE", "EnvType": "string", "EnvValue": "devtroncd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEVTRON_SECRET_NAME", "EnvType": "string", "EnvValue": "devtron-secret", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_CLIENT_ID", "EnvType": "string", "EnvValue": "argo-cd", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_HOST", "EnvType": "string", "EnvValue": "http://localhost", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_PORT", "EnvType": "string", "EnvValue": "5556", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "DEX_SCOPES", "EnvType": "", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "IDLE_TIMEOUT_SECS", "EnvType": "int", "EnvValue": "60", "EnvDescription": "this is the timeout that is set in the IdleTimeout value for http.Server", "Example": "", "Deprecated": "false"}, {"Env": "INTERNAL_PORT", "EnvType": "int", "EnvValue": "9090", "EnvDescription": "port on which the license-manager internal API server runs", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LICENSE_FREE_TRAIL_DAYS", "EnvType": "int64", "EnvValue": "14", "EnvDescription": "number of days for which we are giving free trail", "Example": "", "Deprecated": "false"}, {"Env": "LICENSE_GRACE_PERIOD_SECS", "EnvType": "int64", "EnvValue": "0", "EnvDescription": "this is the grace period in seconds for the license, this is used to allow user to use the application for some time after the license is expired", "Example": "", "Deprecated": "false"}, {"Env": "LICENSE_MANAGER_API_SECRET", "EnvType": "string", "EnvValue": "", "EnvDescription": "this is used to authenticate the api calls where api-secret header should be passed to api which admin of license manager can control", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PORT", "EnvType": "int", "EnvValue": "8080", "EnvDescription": "port on which the license-manager API server runs", "Example": "", "Deprecated": "false"}, {"Env": "REMINDER_THRESHOLD_FOR_FREE_TRIAL", "EnvType": "int", "EnvValue": "3", "EnvDescription": "this is used to set ( to be remind in time) for free trial", "Example": "", "Deprecated": "false"}, {"Env": "REMINDER_THRESHOLD_FOR_LICENSE", "EnvType": "int", "EnvValue": "15", "EnvDescription": "this is used to set ( to be remind in time) for license", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USER_SESSION_DURATION_SECONDS", "EnvType": "int", "EnvValue": "86400", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "WEBHOOK_HTTP_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "HTTP timeout for webhook API calls in seconds", "Example": "", "Deprecated": "false"}, {"Env": "WEBHOOK_PLATFORM_CONFIGS_JSON", "EnvType": "string", "EnvValue": "", "EnvDescription": "JSON mapping of platforms to their configuration (webhook URLs and access tokens)", "Example": "", "Deprecated": "false"}]}, {"Category": "POSTGRES", "Fields": [{"Env": "APP", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "Application name", "Example": "", "Deprecated": "false"}, {"Env": "CASBIN_DATABASE", "EnvType": "string", "EnvValue": "casbin", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_ADDR", "EnvType": "string", "EnvValue": "127.0.0.1", "EnvDescription": "address of postgres service", "Example": "postgresql-postgresql.devtroncd", "Deprecated": "false"}, {"Env": "PG_DATABASE", "EnvType": "string", "EnvValue": "orchestrator", "EnvDescription": "postgres database to be made connection with", "Example": "orchestrator, casbin, git_sensor, lens", "Deprecated": "false"}, {"Env": "PG_PASSWORD", "EnvType": "string", "EnvValue": "", "EnvDescription": "password for postgres, associated with PG_USER", "Example": "confidential ;)", "Deprecated": "false"}, {"Env": "PG_PORT", "EnvType": "string", "EnvValue": "5432", "EnvDescription": "port of postgresql service", "Example": "5432", "Deprecated": "false"}, {"Env": "PG_READ_TIMEOUT", "EnvType": "int64", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_USER", "EnvType": "string", "EnvValue": "", "EnvDescription": "user for postgres", "Example": "postgres", "Deprecated": "false"}, {"Env": "PG_WRITE_TIMEOUT", "EnvType": "int64", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}]