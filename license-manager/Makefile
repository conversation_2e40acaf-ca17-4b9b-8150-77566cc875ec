all: fetch-all-env wire build

TARGET_BRANCH?=main
dep-update-oss:
	go mod edit -replace=github.com/devtron-labs/common-lib=github.com/devtron-labs/devtron-services/common-lib@$(TARGET_BRANCH) \
			    -replace=github.com/devtron-labs/authenticator=github.com/devtron-labs/devtron-services/authenticator@$(TARGET_BRANCH)
	go mod tidy
	go mod vendor


dep-update-ent:
	go mod edit --replace=github.com/devtron-labs/common-lib-private=github.com/devtron-labs/devtron-services-enterprise/common-lib@$(TARGET_BRANCH)
	go mod tidy
	go mod vendor

wire:
	wire

build:
	go build -o license-manager .

fetch-all-env:
	go run fetchAllEnv/fetchAllEnv.go
