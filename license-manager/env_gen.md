

## DEVTRON Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | AWS_REGION | string | | aws region | ap-south-1 | false |
 | AWS_SECRET_MANAGER_CONFIG | string | | aws secret manager config to get private key and shared secret | {'accessKey'':'xxx','passkey':'xxx','region':'xxx'} | false |
 | AWS_SECRET_NAME | string | | aws secret name to get private key and shared secret | devtron-license-manager-secret | false |
 | DEVTRON_DEFAULT_NAMESPACE | string |devtroncd |  |  | false |
 | DEVTRON_DEX_SECRET_NAMESPACE | string |devtroncd |  |  | false |
 | DEVTRON_SECRET_NAME | string |devtron-secret |  |  | false |
 | DEX_CLIENT_ID | string |argo-cd |  |  | false |
 | DEX_HOST | string |http://localhost |  |  | false |
 | DEX_PORT | string |5556 |  |  | false |
 | DEX_SCOPES |  | |  |  | false |
 | IDLE_TIMEOUT_SECS | int |60 | this is the timeout that is set in the IdleTimeout value for http.Server |  | false |
 | INTERNAL_PORT | int |9090 | port on which the license-manager internal API server runs |  | false |
 | K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST | int |25 |  |  | false |
 | K8s_TCP_IDLE_CONN_TIMEOUT | int |300 |  |  | false |
 | K8s_TCP_KEEPALIVE | int |30 |  |  | false |
 | K8s_TCP_TIMEOUT | int |30 |  |  | false |
 | K8s_TLS_HANDSHAKE_TIMEOUT | int |10 |  |  | false |
 | LICENSE_FREE_TRAIL_DAYS | int64 |14 | number of days for which we are giving free trail |  | false |
 | LICENSE_GRACE_PERIOD_SECS | int64 |0 | this is the grace period in seconds for the license, this is used to allow user to use the application for some time after the license is expired |  | false |
 | LICENSE_MANAGER_API_SECRET | string | | this is used to authenticate the api calls where api-secret header should be passed to api which admin of license manager can control |  | false |
 | LOG_LEVEL | int |0 |  |  | false |
 | PG_EXPORT_PROM_METRICS | bool |true |  |  | false |
 | PG_LOG_ALL_FAILURE_QUERIES | bool |true |  |  | false |
 | PG_LOG_ALL_QUERY | bool |false |  |  | false |
 | PG_LOG_SLOW_QUERY | bool |true |  |  | false |
 | PG_QUERY_DUR_THRESHOLD | int64 |5000 |  |  | false |
 | PORT | int |8080 | port on which the license-manager API server runs |  | false |
 | REMINDER_THRESHOLD_FOR_FREE_TRIAL | int |3 | this is used to set ( to be remind in time) for free trial |  | false |
 | REMINDER_THRESHOLD_FOR_LICENSE | int |15 | this is used to set ( to be remind in time) for license |  | false |
 | RUNTIME_CONFIG_LOCAL_DEV | LocalDevMode |false |  |  | false |
 | USER_SESSION_DURATION_SECONDS | int |86400 |  |  | false |
 | USE_CUSTOM_HTTP_TRANSPORT | bool |false |  |  | false |
 | WEBHOOK_HTTP_TIMEOUT | int |10 | HTTP timeout for webhook API calls in seconds |  | false |
 | WEBHOOK_PLATFORM_CONFIGS_JSON | string | | JSON mapping of platforms to their configuration (webhook URLs and access tokens) |  | false |


## POSTGRES Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | APP | string |orchestrator | Application name |  | false |
 | CASBIN_DATABASE | string |casbin |  |  | false |
 | PG_ADDR | string |127.0.0.1 | address of postgres service | postgresql-postgresql.devtroncd | false |
 | PG_DATABASE | string |orchestrator | postgres database to be made connection with | orchestrator, casbin, git_sensor, lens | false |
 | PG_PASSWORD | string | | password for postgres, associated with PG_USER | confidential ;) | false |
 | PG_PORT | string |5432 | port of postgresql service | 5432 | false |
 | PG_READ_TIMEOUT | int64 |30 |  |  | false |
 | PG_USER | string | | user for postgres | postgres | false |
 | PG_WRITE_TIMEOUT | int64 |30 |  |  | false |

