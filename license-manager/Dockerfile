FROM golang:1.23 AS build-env

RUN apt update && \
    apt install git gcc musl-dev make -y && \
    go install github.com/google/wire/cmd/wire@latest

WORKDIR /go/src/github.com/devtron-labs/license-manager

ADD . /go/src/github.com/devtron-labs/license-manager/

RUN GOOS=linux make

FROM ubuntu:24.04@sha256:72297848456d5d37d1262630108ab308d3e9ec7ed1c3286a32fe09856619a782

RUN apt update && \
    apt install ca-certificates -y && \
    apt clean autoclean && \
    apt autoremove -y && rm -rf /var/lib/apt/lists/* && \
    useradd -ms /bin/bash devtron

COPY --chown=devtron:devtron --from=build-env  /go/src/github.com/devtron-labs/license-manager/license-manager .

COPY --chown=devtron:devtron blacklistedDomains.csv .

USER devtron

CMD ["./license-manager"]
