openapi: 3.0.0
info:
  title: License SSO API
  description: API for fetching the list of SSO providers
  version: 1.0.0
servers:
  - url: https://devtron-ent-1.devtron.info
paths:
  /license-manager/sso/list:
    get:
      summary: Get list of SSO providers
      description: Returns a list of configured SSO providers.
      operationId: getSSOList
      responses:
        '200':
          description: Successful response with a list of SSO providers
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: OK
                  result:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 2
                        name:
                          type: string
                          example: microsoft
                        url:
                          type: string
                          format: uri
                          example: https://devtron-ent-1.devtron.info/orchestrator
                        active:
                          type: boolean
                          example: true
                        globalAuthConfigType:
                          type: string
                          example: ""

  /license-manager/auth/login:
    get:
      summary: Perform login
      description: Initiates the login process and redirects to the dashboard.
      operationId: login
      parameters:
        - name: return_url
          in: query
          required: true
          description: The URL to redirect to after login
          schema:
            type: string
            format: uri
            example: https://devtron-ent-1.devtron.info/orchestrator/dashboard
      responses:
        '302':
          description: Redirect to the specified return_url on successful login
        '401':
          description: Unauthorized access
        '400':
          description: Bad request
  /license-manager/auth/verify:
    get:
      summary: Verify authentication
      description: Verifies if the authentication token is valid.
      operationId: verifyAuth
      responses:
        '200':
          description: Authentication verified successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    example: 200
                  status:
                    type: string
                    example: OK
                  result:
                    type: object
                    properties:
                      emailId:
                        type: string
                        format: email
                        example: <EMAIL>
                      isVerified:
                        type: boolean
                        example: true
                      userId:
                        type: integer
                        example: 1
        '401':
          description: Unauthorized access
        '400':
          description: Bad request

