openapi: 3.0.3
info:
  title: License Key API
  description: API to generate and validate a license key based on a fingerprint or an existing license key
  version: 1.0.0
servers:
  - url: https://api.example.com
    description: Production server
paths:
  /license-manager/license:
    post:
      summary: Generate a License Key
      description: Generates a license key based on the provided fingerprint or existing license key.
      operationId: generateLicenseKey
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicenseRequest'
      responses:
        "200":
          description: Successfully generated license key
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicensePostResponse'
        "400":
          description: Invalid request
        "500":
          description: Internal server error
    get:
      summary: Retrieve a License Key
      description: Retrieves the license data for a given license key.
      operationId: getLicense
      parameters:
        - name: licenseKey
          in: query
          required: false
          schema:
            type: string
          description: The license key to retrieve details for.
      responses:
        "200":
          description: Successfully retrieved license key details
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicenseGetResponse'
        "404":
          description: License key not found
        "500":
          description: Internal server error
  /license-manager/fingerprint/validate:
    post:
      summary: Validate a Fingerprint
      description: Validates if the provided fingerprint is associated with a valid license.
      operationId: validateFingerprint
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                fingerprint:
                  type: string
                  description: The fingerprint to be validated
              required:
                - fingerprint
      responses:
        "200":
          description: Successfully validated fingerprint
          content:
            application/json:
              schema:
                type: object
                properties:
                  isValid:
                    type: boolean
                    description: Indicates if the fingerprint is valid
        "400":
          description: Invalid request
        "500":
          description: Internal server error
components:
  schemas:
    LicenseRequest:
      type: object
      properties:
        fingerprint:
          type: string
          description: The fingerprint generated by the requesting client
        userDetails:
          type: object
          description: Details of the user requesting the license
          properties:
            firstName:
              type: string
              description: The first name of the user
            lastName:
              type: string
              description: The last name of the user
            country:
              type: string
              description: The country of the user
            countryCode:
              type: string
              description: The country code of the user
            phoneNumber:
              type: string
              description: The phone number of the user
            companyName:
              type: string
              description: The company name of the user
            companyHeadquarter:
              type: string
              description: The headquarters location of the company
            jobTitle:
              type: string
              description: The job title of the user
      required:
        - fingerprint
    LicenseGetResponse:
      type: object
      properties:
        license:
          type: string
          description: Base64 encoded JSON string of LicenseData
        fingerprint:
          type: string
          description: installation fingerprint of devtron
        isTrial:
          type: boolean
          description: denoting if the license is a trial license
        expiry:
          type: string
          format: date-time
          description: Time at which the license is going to expire in UTC
        ttl:
          type: integer
          description: Time to live in seconds
        gracePeriod:
          type: integer
          description: Grace period in seconds
        reminderThreshold:
          type: integer
          description: reminder threshold signifies time after which reminder to be shown in days.
        claimedByUserDetails:
          type: object
          properties:
            firstName:
              type: string
              description: First name of the user
            lastName:
              type: string
              description: Last name of the user
            email:
              type: string
              description: Email of the user
        organisationMetadata:
          type: object
          description: metadata of the organisation
          properties:
            name:
              type: string
              description: name of the organisation
            domain:
              type: string
              description: domain of the organisation
    LicensePostResponse:
      type: object
      properties:
        license:
          type: string
          description: Base64 encoded JSON string of LicenseData