openapi: 3.0.0
info:
  title: License Manager User API
  version: 1.0.0
tags:
  - name: User
    description: User-related operations
paths:
  /license-manager/user:
    get:
      summary: Get Logged-In User Metadata
      operationId: getLoggedInUserMetadata
      tags:
        - User
      responses:
        "200":
          description: Successfully retrieved user metadata
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserDetailsDto"
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
  /license-manager/user/metadata:
    post:
      summary: Update Logged-In User Metadata
      operationId: updateLoggedInUserMetadata
      tags:
        - User
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UserDetailsDto"
      responses:
        "200":
          description: Successfully updated user metadata
        "400":
          description: Bad request
        "401":
          description: Unauthorized
        "403":
          description: Forbidden
components:
  schemas:
    UserDetailsDto:
      type: object
      properties:
        userDetails:
          $ref: "#/components/schemas/UserDetails"
    UserDetails:
      type: object
      properties:
        firstName:
          type: string
          description: First name of the user
        lastName:
          type: string
          description: Last name of the user
        phoneNumber:
          type: string
          description: The phone number of the user
        companyName:
          type: string
          description: The company name of the user
        companyHeadquarter:
          type: string
          description: The headquarters location of the company
        jobTitle:
          type: string
          description: The job title of the user