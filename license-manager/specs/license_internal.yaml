openapi: 3.0.3
info:
  title: License Key API
  description: API to generate and validate a license key based on a fingerprint or an existing license key.
  version: 1.0.0
servers:
  - url: https://localhost:9090
    description: Production server
paths:
  /license-manager/license:
    post:
      summary: Generate a License Key
      description: Generates a license key based on the provided fingerprint or existing license key.
      operationId: generateLicenseKey
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LicenseUploadRequest'
      responses:
        "200":
          description: Successfully generated license key
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LicensePostResponse'
        "400":
          description: Invalid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  schemas:
    LicenseUploadRequest:
      type: object
      properties:
        fingerPrint:
          type: string
          description: |
            Base64-encoded JSON string of LicenseRequest.  
            Internally used as LicenseRequest but represented as `fingerPrint` in JSON.
        expiryInDays:
          type: integer
          format: int64
          description: The number of days until the license expires.
        gracePeriodInDays:
          type: integer
          format: int64
          description: The grace period (in days) after expiration.
        orgAdminEmail:
          type: string
          format: email
          description: The email of the organization admin requesting the license.
        userDetails:
          $ref: '#/components/schemas/UserDetails'
      required:
        - fingerPrint
        - expiryInDays
        - orgAdminEmail
        - userDetails

    UserDetails:
      type: object
      description: Details of the user requesting the license.
      properties:
        firstName:
          type: string
          description: The first name of the user.
        lastName:
          type: string
          description: The last name of the user.
        phoneNumber:
          type: string
          description: The phone number of the user.
        companyName:
          type: string
          description: The company name of the user.
        companyHeadquarter:
          type: string
          description: The headquarters location of the company.
        jobTitle:
          type: string
          description: The job title of the user.
      required:
        - companyName

    LicensePostResponse:
      type: object
      properties:
        license:
          type: string
          description: Base64-encoded JSON string of LicenseData.

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: HTTP status code.
        message:
          type: string
          description: Error message.
      required:
        - code
        - message