BEGIN;
CREATE TABLE IF NOT EXISTS organisations (
            id SERIAL PRIMARY KEY,
            name TEXT NOT NULL UNIQUE,
            metadata JSONB,
            created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO organisations (name, created_on)
VALUES ('devtron' , now())
    ON CONFLICT (name) DO NOTHING;

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    email_id TEXT UNIQUE NOT NULL,
    org_id INTEGER REFERENCES organisations(id) ON DELETE CASCADE,
    is_admin BOOLEAN NOT NULL DEFAULT FALSE,
    metadata JSONB,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (email_id, org_id, is_admin, created_on)
SELECT 'admin', id, TRUE, now()
FROM organisations
WHERE name = 'devtron'
    ON CONFLICT (email_id) DO NOTHING;

CREATE TABLE IF NOT EXISTS fingerprints (
    id SERIAL PRIMARY KEY,
    fingerprint TEXT UNIQUE NOT NULL,
    metadata JSONB NOT NULL,
    org_id INTEGER REFERENCES organisations(id) ON DELETE CASCADE,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS licenses (
    id SERIAL PRIMARY KEY,
    license_key TEXT UNIQUE NOT NULL,
    license_data TEXT NOT NULL,
    fingerprint_id INTEGER REFERENCES fingerprints(id) ON DELETE CASCADE,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS license_certs (
    id SERIAL PRIMARY KEY,
    license_id INTEGER REFERENCES licenses(id) ON DELETE CASCADE,
    cert    TEXT NOT NULL,
    cert_data JSONB NOT NULL,
    created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER REFERENCES users(id),
    active BOOLEAN DEFAULT TRUE
);

CREATE TABLE IF NOT EXISTS user_license_request_audit (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    license_key_id INTEGER ,
    request_status TEXT CHECK (request_status IN ('success', 'in_progress', 'failed')) NOT NULL,
    metadata JSONB,
    cert_id INTEGER ,
    requested_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
COMMIT;