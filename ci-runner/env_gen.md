

## DEVTRON Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | AZURE_ACCOUNT_KEY | string | |  |  | false |
 | AZURE_ACCOUNT_NAME | string | |  |  | false |
 | AZURE_BLOB_CONTAINER_CI_CACHE | string | |  |  | false |
 | AZURE_BLOB_CONTAINER_CI_LOG | string | |  |  | false |
 | AZURE_GATEWAY_CONNECTION_INSECURE | bool |true |  |  | false |
 | AZURE_GATEWAY_URL | string | |  |  | false |
 | BLOB_STORAGE_GCP_CREDENTIALS_JSON | string | |  |  | false |
 | BLOB_STORAGE_PROVIDER |  | |  |  | false |
 | BLOB_STORAGE_S3_ACCESS_KEY | string | |  |  | false |
 | BLOB_STORAGE_S3_BUCKET_VERSIONED | bool |true |  |  | false |
 | BLOB_STORAGE_S3_ENDPOINT | string | |  |  | false |
 | BLOB_STORAGE_S3_ENDPOINT_INSECURE | bool |false |  |  | false |
 | BLOB_STORAGE_S3_SECRET_KEY | string | |  |  | false |
 | CONSUMER_CONFIG_JSON | string | |  |  | false |
 | DEFAULT_BUILD_LOGS_BUCKET | string | |  |  | false |
 | DEFAULT_CACHE_BUCKET | string | |  |  | false |
 | DEFAULT_CACHE_BUCKET_REGION | string | |  |  | false |
 | DEFAULT_CD_LOGS_BUCKET_REGION | string | |  |  | false |
 | DEFAULT_LOG_TIME_LIMIT | int64 |1 |  |  | false |
 | IMAGE_SCANNER_ENDPOINT | string |http://image-scanner-new-demo-devtroncd-service.devtroncd:80 |  |  | false |
 | K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST | int |25 |  |  | false |
 | K8s_TCP_IDLE_CONN_TIMEOUT | int |300 |  |  | false |
 | K8s_TCP_KEEPALIVE | int |30 |  |  | false |
 | K8s_TCP_TIMEOUT | int |30 |  |  | false |
 | K8s_TLS_HANDSHAKE_TIMEOUT | int |10 |  |  | false |
 | LOG_LEVEL | int |0 |  |  | false |
 | NATS_MSG_ACK_WAIT_IN_SECS | int |120 |  |  | false |
 | NATS_MSG_BUFFER_SIZE | int |-1 |  |  | false |
 | NATS_MSG_MAX_AGE | int |86400 |  |  | false |
 | NATS_MSG_PROCESSING_BATCH_SIZE | int |1 |  |  | false |
 | NATS_MSG_REPLICAS | int |0 |  |  | false |
 | NATS_SERVER_HOST | string |nats://devtron-nats.devtroncd:4222 |  |  | false |
 | NO_PROXY | string |registry.hub.docker.com,*.docker.com,*.docker.io,docker.io,registry-1.docker.io,github.com,ghcr.io,pkg-containers.githubusercontent.com |  |  | false |
 | PG_EXPORT_PROM_METRICS | bool |true |  |  | false |
 | PG_LOG_ALL_FAILURE_QUERIES | bool |true |  |  | false |
 | PG_LOG_ALL_QUERY | bool |false |  |  | false |
 | PG_LOG_SLOW_QUERY | bool |true |  |  | false |
 | PG_QUERY_DUR_THRESHOLD | int64 |5000 |  |  | false |
 | RUNTIME_CONFIG_LOCAL_DEV | LocalDevMode |false |  |  | false |
 | SHOW_DOCKER_BUILD_ARGS | bool |true |  |  | false |
 | STREAM_CONFIG_JSON | string | |  |  | false |
 | USE_CUSTOM_HTTP_TRANSPORT | bool |false |  |  | false |

