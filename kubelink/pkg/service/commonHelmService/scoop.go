package commonHelmService

import (
	"context"
	"fmt"
	"github.com/devtron-labs/common-lib-private/utils/k8s/proxy/helper"
	"github.com/devtron-labs/common-lib/utils/k8s"
	client "github.com/devtron-labs/kubelink/grpc"
	"time"
)

func (impl *ResourceTreeServiceExtendedImpl) GetScoopConfig(clusterId int, clusterConfig *k8s.ClusterConfig, cacheConfig *client.CacheConfig) (string, error) {
	timeoutContext, _ := context.WithTimeout(context.Background(), 5*time.Second)

	if cacheConfig == nil || len(cacheConfig.ServiceName) == 0 {
		return "", ScoopNotConfiguredErr
	}
	scoopConfig := ScoopServiceClusterConfig{
		ServiceName: cacheConfig.ServiceName,
		Namespace:   cacheConfig.Namespace,
		PassKey:     cacheConfig.PassKey,
		Port:        cacheConfig.Port,
	}
	restConfig, err := impl.k8sUtil.GetRestConfigByCluster(clusterConfig, k8s.WithDefaultHttpTransport())
	if err != nil {
		return "", err
	}
	v1Client, err := impl.k8sUtil.GetCoreV1ClientByRestConfig(restConfig)
	if err != nil {
		// not logging clusterConfig as it contains sensitive data
		impl.logger.Errorw("error occurred in getting v1Client with cluster config", "err", err, "clusterId", clusterId)
		return "", err
	}
	scoopPort, err := impl.interClusterServiceCommunicationHandler.GetClusterServiceProxyPort(timeoutContext, restConfig, v1Client, helper.NewClusterServiceKey(clusterId, scoopConfig.ServiceName, scoopConfig.Namespace, scoopConfig.Port))
	if err != nil {
		impl.logger.Errorw("error in getting a service proxy port for scoop", "clusterId", clusterId, "err", err)
		return "", err
	}
	scoopUrl := fmt.Sprintf("http://127.0.0.1:%d", scoopPort)
	return scoopUrl, nil
}
