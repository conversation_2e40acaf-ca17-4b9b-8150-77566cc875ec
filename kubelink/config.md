#  KUBELINK PARAMAETER

| Key                              | Value                                       | Description                            |
|----------------------------------|---------------------------------------------|----------------------------------------|
| ENABLE_HELM_RELEASE_CACHE        | true                                        | Enable Helm release cache              |
| MANIFEST_FETCH_BATCH_SIZE        | 2                                           | Manifest fetch batch size              |
| NATS_MSG_PROCESSING_BATCH_SIZE    | 1                                           | NATS message processing batch size     |
| NATS_SERVER_HOST                 | nats://devtron-nats.devtroncd:4222        | NATS server host                        |
| PG_ADDR                          | postgresql-postgresql.devtroncd           | PostgreSQL server address              |
| PG_DATABASE                      | orchestrator                                | PostgreSQL database name                |
| PG_LOG_QUERY                     | true                                        | Enable PostgreSQL query logging        |
| PG_PORT                          | 5432                                        | PostgreSQL server port                  |
| PG_USER                          | postgres                                    | PostgreSQL username                     |
| RUN_HELM_INSTALL_IN_ASYNC_MODE   | true                                        | Run Helm install in asynchronous mode  |
