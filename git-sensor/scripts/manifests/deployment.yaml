apiVersion: v1
kind: Service
metadata:
  name: git-sensor-service
  labels:
    app: git-sensor
spec:
  ports:
    - port: 80
      name: sensor
      targetPort: 8080
  selector:
    app: git-sensor
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: git-sensor
spec:
  selector:
    matchLabels:
      app: git-sensor # has to match .spec.template.metadata.labels
  serviceName: "git-sensor"
  replicas: 1 # by default is 1
  template:
    metadata:
      labels:
        app: git-sensor # has to match .spec.selector.matchLabels
    spec:
      terminationGracePeriodSeconds: 10
      containers:
        - name: git-sensor
          image: git-sensor:9-e3e3e1a
          ports:
            - containerPort: 8080
              name: sensor
          volumeMounts:
            - name: git-volume
              mountPath: /git-base/
          env:
          - name: PG_ADDR
            value: postgresql-postgresql.devtroncd
  volumeClaimTemplates:
    - metadata:
        name: git-volume
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: "gp2"
        resources:
          requests:
            storage: 10Gi
