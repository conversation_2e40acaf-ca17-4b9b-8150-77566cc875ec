FROM golang:1.18 AS build-env

RUN apt update && \
	apt install git gcc musl-dev make -y && \
	go install github.com/google/wire/cmd/wire@latest

WORKDIR /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin
#make sure devtron's repo and devtron-services-enterprise repo are in same level in the file structure

#copying casbin's code from devtron-services-enterprise repo to working directory of the container
ADD ./devtron-services-enterprise/casbin /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin/

#copying casbin's migration from devtron's repo to working directory of the container
ADD ./devtron/scripts/casbin /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin/scripts/casbin
RUN GOOS=linux make

FROM ubuntu:24.04@sha256:72297848456d5d37d1262630108ab308d3e9ec7ed1c3286a32fe09856619a782
RUN apt update && \
	apt install ca-certificates -y && \
	apt clean autoclean && \
	apt autoremove -y && rm -rf /var/lib/apt/lists/* && \
 	useradd -ms /bin/bash devtron
 
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin/auth_model.conf .
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin .
COPY --from=build-env  /go/src/github.com/devtron-labs/devtron-services-enterprise/casbin/scripts/casbin ./scripts/casbin

RUN chown -R devtron:devtron ./casbin-enterprise && \
	chown -R devtron:devtron ./auth_model.conf
USER devtron

CMD ["./casbin-enterprise"]
