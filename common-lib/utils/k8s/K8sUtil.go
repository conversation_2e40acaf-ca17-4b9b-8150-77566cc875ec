/*
 * Copyright (c) 2020 Devtron Labs
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package k8s

import (
	"github.com/devtron-labs/common-lib-private/utils/ssh"
	k8sUtil "github.com/devtron-labs/common-lib/utils/k8s"
	remoteConnectionBean "github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
	"go.uber.org/zap"
)

type K8sUtilExtended struct {
	k8sUtil.K8sService
	sshTunnelWrapperService ssh.SSHTunnelWrapperService
	logger                  *zap.SugaredLogger
}

func NewK8sUtilExtended(logger *zap.SugaredLogger, runTimeConfig *k8sUtil.RuntimeConfig,
	sshTunnelWrapperService ssh.SSHTunnelWrapperService) (*K8sUtilExtended, error) {
	k8sService, err := k8sUtil.NewK8sUtilBuilder(logger, runTimeConfig, NewKubeConfigExtendedBuilder(logger, sshTunnelWrapperService))
	if err != nil {
		logger.Errorw("error in getting NewK8sUtil", "err", err)
		return nil, err
	}
	return &K8sUtilExtended{
		K8sService:              k8sService,
		sshTunnelWrapperService: sshTunnelWrapperService,
		logger:                  logger,
	}, nil
}

func (impl *K8sUtilExtended) CleanupForClusterUsedForVerification(config *k8sUtil.ClusterConfig) {
	//cleanup for ssh tunnel, as other methods do not require cleanup
	if config.RemoteConnectionConfig != nil && config.RemoteConnectionConfig.ConnectionMethod == remoteConnectionBean.RemoteConnectionMethodSSH {
		impl.sshTunnelWrapperService.CleanupForVerificationCluster(config.ClusterName)
	}
}
