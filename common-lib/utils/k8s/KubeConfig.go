/*
 * Copyright (c) 2020 Devtron Labs
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package k8s

import (
	"fmt"
	"github.com/devtron-labs/common-lib-private/sshTunnel/bean"
	"github.com/devtron-labs/common-lib-private/utils/ssh"
	k8sUtil "github.com/devtron-labs/common-lib/utils/k8s"
	remoteConnectionBean "github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
	"go.uber.org/zap"
	"k8s.io/client-go/rest"
	"net/http"
	"net/url"
)

func (impl *KubeConfigExtendedBuilder) getHostUrlForSSHTunnelConfiguredCluster(clusterConfig *k8sUtil.ClusterConfig) (string, error) {
	var sshTunnelUrl string
	//getting port
	port, err := impl.sshTunnelWrapperService.GetPortUsedForACluster(clusterConfig)
	if err != nil {
		impl.logger.Errorw("error in getting port of ssh tunnel connected cluster", "err", err, "clusterId", clusterConfig.ClusterId)
		return sshTunnelUrl, err
	}
	sshTunnelUrl = fmt.Sprintf("https://%s:%d", bean.LocalHostAddress, port)
	return sshTunnelUrl, nil
}

type KubeConfigExtendedBuilder struct {
	*k8sUtil.KubeConfigBuilder
	logger                  *zap.SugaredLogger
	sshTunnelWrapperService ssh.SSHTunnelWrapperService
}

func NewKubeConfigExtendedBuilder(
	logger *zap.SugaredLogger,
	sshTunnelWrapperService ssh.SSHTunnelWrapperService,
) *KubeConfigExtendedBuilder {
	return &KubeConfigExtendedBuilder{
		KubeConfigBuilder:       k8sUtil.NewKubeConfigBuilder(),
		logger:                  logger,
		sshTunnelWrapperService: sshTunnelWrapperService,
	}
}

// BuildKubeConfigForCluster builds a kubeconfig for the given cluster configuration.
// This function is used in KubeConfigExtended for extended implementation.
func (impl *KubeConfigExtendedBuilder) BuildKubeConfigForCluster(clusterConfig *k8sUtil.ClusterConfig) (*rest.Config, error) {
	restConfig, err := impl.KubeConfigBuilder.BuildKubeConfigForCluster(clusterConfig)
	if err != nil {
		impl.logger.Errorw("error in getting rest config for cluster", "err", err, "clusterName", clusterConfig.ClusterName)
		return nil, err
	}
	connectionConfig := clusterConfig.RemoteConnectionConfig
	if connectionConfig != nil && connectionConfig.ConnectionMethod != remoteConnectionBean.RemoteConnectionMethodDirect {
		if connectionConfig.SSHTunnelConfig != nil && connectionConfig.ConnectionMethod == remoteConnectionBean.RemoteConnectionMethodSSH {
			hostUrl, err := impl.getHostUrlForSSHTunnelConfiguredCluster(clusterConfig)
			if err != nil {
				impl.logger.Errorw("error in getting hostUrl for ssh configured cluster", "err", err, "clusterId", clusterConfig.ClusterId)
				return nil, err
			}
			// Override the server URL with the localhost URL where the SSH tunnel is hosted
			restConfig.Host = hostUrl
		} else if connectionConfig.ProxyConfig != nil && connectionConfig.ConnectionMethod == remoteConnectionBean.RemoteConnectionMethodProxy {
			proxyUrl := connectionConfig.ProxyConfig.ProxyUrl
			proxy, err := url.Parse(proxyUrl)
			if err != nil {
				impl.logger.Errorw("error in parsing proxy url", "err", err, "proxyUrl", proxyUrl)
				return nil, err
			}
			restConfig.Proxy = http.ProxyURL(proxy)
		} else {
			impl.logger.Errorw("error in fetching connection config", "err", err)
			return nil, err
		}
	}
	return restConfig, nil
}
