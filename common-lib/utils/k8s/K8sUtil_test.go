/*
 * Copyright (c) 2020 Devtron Labs
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

package k8s

import (
	utils2 "github.com/devtron-labs/common-lib/utils"
	"net/http"
	"testing"

	"github.com/devtron-labs/common-lib/utils/k8s"
	"github.com/devtron-labs/common-lib/utils/remoteConnection/bean"
)

func TestBuildKubeConfigForCluster_ProxyConfig(t *testing.T) {
	// Initialize logger
	logger, _ := utils2.NewSugardLogger()

	// Create a dummy ClusterConfig
	clusterConfig := &k8s.ClusterConfig{
		ClusterName:           "dummy-cluster",
		ClusterId:             10,
		InsecureSkipTLSVerify: true,
		RemoteConnectionConfig: &bean.RemoteConnectionConfigBean{
			ConnectionMethod: bean.RemoteConnectionMethodProxy,
			ProxyConfig: &bean.ProxyConfig{
				ProxyUrl: "http://proxy.example.com:8080",
			},
		},
	}
	// Initialize the K8sUtilExtended
	runtimeCfg, _ := k8s.GetRuntimeConfig()
	runtimeCfg.LocalDevMode = true
	k8sUtilExtended, err := NewK8sUtilExtended(logger, runtimeCfg, nil)
	if err != nil {
		t.Fatalf("Error initializing K8sUtilExtended: %v", err)
	}
	restConfig, err := k8sUtilExtended.GetRestConfigByCluster(clusterConfig)
	if err != nil {
		t.Fatalf("Error getting rest config: %v", err)
	}
	if restConfig == nil {
		t.Fatal("Expected non-nil rest config")
	}
	// Check if the URL is set correctly
	if restConfig.Proxy == nil {
		t.Fatal("Expected non-nil proxy config")
	}
	expectedURL := "http://proxy.example.com:8080"
	httpReq, _ := http.NewRequest("GET", expectedURL, nil)
	gotUrl, _ := restConfig.Proxy(httpReq)
	if gotUrl.String() != expectedURL {
		t.Fatalf("Expected proxy URL %s, got %s", expectedURL, gotUrl.String())
	}
}
