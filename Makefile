
TARGET_BRANCH?=develop

all: build

dep-update-oss:
	cd git-sensor && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd chart-sync && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd ci-runner && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd common-lib && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd kubelink && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd scoop && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd image-scanner && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-oss
	cd license-manager && TARGET_BRANCH=$(TARGET_BRANCH) $(<PERSON>KE) dep-update-oss

dep-update-ent:
	cd chart-sync && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent
	cd ci-runner && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent
	cd kubelink && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent
	cd scoop && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent
	cd image-scanner && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent
	cd license-manager && TARGET_BRANCH=$(TARGET_BRANCH) $(MAKE) dep-update-ent

build:
	cd casbin && $(MAKE)
	cd chart-sync && $(MAKE)
	cd ci-runner && $(MAKE)
#	cd devtctl && $(MAKE)
	cd git-sensor && $(MAKE)
	cd kubelink && $(MAKE)
	cd scoop && $(MAKE)
	cd image-scanner && $(MAKE)
	cd license-manager && $(MAKE)
#	cd common-lib && $(MAKE)
