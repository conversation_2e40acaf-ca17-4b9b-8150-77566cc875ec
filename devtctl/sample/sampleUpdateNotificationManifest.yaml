#apiVersion: v1
#spec:
#  payload:
#    - criteria:
#       - notificationUpdateRequest:
#           updateType: rules
#           notificationConfigRequest:
#             - id: 142
#               event:
#                 success: false
#                 trigger: true
#                 failure: false
#               providers:
#                 - dest: webhook
#                   configId: 55
#         severity:
#           critical: true
#           moderate: true
#           low: true
apiVersion: v1
spec:
  payload:
    - criteria:
        - notificationUpdateRequest:
            updateType: events
            notificationConfigRequest:
              - id: 102
                event:
                  failure: true
                  success: true
                  imagescanner: true
                  trigger: true
                providers:
                  - dest: webhook
                    configId: 15
          severity:
            critical: true
            moderate: true
            low: true


