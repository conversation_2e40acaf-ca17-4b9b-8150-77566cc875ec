apiVersion: v1
kind: CD
metadata:
  type: YAML_DOWNLOAD
spec:
  deploymentWindowProfile:
    id: 6
    name: <PERSON><PERSON><PERSON><PERSON><PERSON>
    description: <PERSON><PERSON><PERSON><PERSON><PERSON>
    displayMessage: <PERSON><PERSON><PERSON><PERSON><PERSON>_Daily
    excludedUsersEmails: ["<EMAIL>","<EMAIL>","<EMAIL>"]
    isUserExcluded: true
    isSuperAdminExcluded: true
    type: BLACKOUT
    deploymentWindowList:
      - timeFrom: 0001-01-01T00:00:00Z
        hourMinuteFrom: "12:41"
        hourMinuteTo: "18:51"
        dayTo: 0
        timeTo: 0001-01-01T00:00:00Z
        dayFrom: 0
        weekdayFrom: ""
        weekdayTo: ""
        frequency: DAILY
        weekdays: []
    timeZone: Asia/Kolkata
    enabled: true
  applyPolicy:
    appEnvNameList:
      - appNames:
          - "newapp-1"
          - "newapp-2"
        envNames:
          - "testsubs"
          - "testsubs1"
          - "testsubs2"
      - appNames:
          - "app-1"
          - "app-2"
        envNames:
          - "test"
          - "test2"
          - "test3"