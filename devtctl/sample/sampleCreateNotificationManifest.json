{"apiVersion": "v1", "spec": {"payload": [{"criteria": [{"webhookConfig": [{"webhookUrl": "https://example.com/webhook", "configName": "TestWebhook", "header": {"Content-Type": "application/json", "Authorization": "Bearer xyz123", "api-token": "eyJhbGciOiJIUzI1NiIsInR5cCI6I…………"}, "payload": "{\"dockerImage\": \"{{devtronContainerImageRepo}}:{{devtronContainerImageTag}}\"}", "description": "Description for webhook configuration"}, {"webhookUrl": "https://example2.com/webhook", "configName": "TestWebhook2", "header": {"Content-Type": "application/json", "Authorization": "Bearer xyz1234567", "api-token": "eyJhbGciOiJIUzI1NiIsInR5cCI6I…………"}, "payload": "{\"dockerImage\": \"{{devtronContainerImageRepo}}:{{devtronContainerImageTag}}\"}", "description": "Description for webhook configuration2"}], "notificationRequest": {"notificationConfigRequest": [{"appNames": ["test"], "pipelineType": "CI", "event": {"success": true}}]}, "severity": {"critical": true, "moderate": false, "low": false}}]}]}}