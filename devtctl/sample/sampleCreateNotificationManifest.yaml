apiVersion: v1
spec:
  payload:
    - criteria:
        - webhookConfig:
            - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
              configName: "kushtest"
              header:
                Content-Type: application/json
              payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
              description: Description for webhook configuration
          notificationRequest:
            notificationConfigRequest:
              - appNames:
                  - test
                pipelineType: CI
                event:
                  imageScanner: true
          severity:
            critical: true
            moderate: true
            low: true



#apiVersion: v1
#spec:
#  payload:
#    - criteria:
#          - configId: 1
#            notificationRequest:
#              notificationConfigRequest:
#                - teamNames:
#                   - devtron-demo
#                  appNames:
#                   - test-app-1
#                  envNames:
#                   - devtron-14-env-2
#                  pipelineType: CI
#                  eventTypeIds:
#                    - 2
#                    - 3
#            severity:
#             critical: true
#             medium: false
#             low: false
#          - configId:
#              - 37
#              - 38
