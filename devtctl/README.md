

# Setup instruction
1. Download and unpack OS specific binary from git repo.
2. add alias devtctl to bashrc/zshrc to the downloaded path
    - copy the path of downloaded binary
    - `vim ~/.zshrc or vim ~/.bashrc` (check the default set by running echo $0 )
    - add line  `alias devtctl="Your binary path/devtctl"`
    - save and run `source ~/.bashrc` or `source ~/.zshrc` or restart your terminal
4. create devtctl.env `vim devtctl.env` file at the path of binary with the following keys
5.     SERVER_URL= "https://subhas.devtron.info/"
       API_TOKEN="your API token"  


7. enable developer options on mac for terminal if not already enabled (System preferences -> security&Privacy -> Privacy tab -> Developer tools -> enable terminal here)

# Help section
    devtctl --help  
    devtctl getCI --help  
    devtctl applyCI --help  

# Sample commands for getCI and applyCI

## To download CI config passing criteria through flags
    devtctl getCI --pipelineIds="1 2 3"  
    devtctl getCI --appIds="1 2 3"  
    devtctl getCI --appNames="app1 app2"  
    devtctl getCI --projectNames="project1 project2"  
    devtctl getCI --envNames="env1 env2"  

All these flags can be used in any combinations. In the sample inputManifest files, there are additional, more complex criteria that users can utilize: `includesAppNames` and `excludesAppNames`. These criteria use regular expressions to search for and filter apps based on their app names.

Let's explain these criteria in a more straightforward way for users to easily understand:

`includesAppNames`: This criteria allows you to specify a regular expression to filter apps based on their app names. If you define includesAppNames, it works similarly to using the --appNames flag via the command-line interface (CLI). By setting includesAppNames, you will receive all the apps whose app names match the provided regular expression.

`excludesAppNames`: In addition to the `includesAppNames`, this criteria allows you to further refine the selection by excluding certain apps. If you use excludesAppNames along with all other criteria, the included apps from `includesAppNames` plus all other criteria mentioned are evaluated first. Then, based on the excludesAppNames regex, any apps with matching app names are excluded from the final result.

To specify the format of output use flag

    --format=json   
    --format=yaml    
(yaml is default)  
To get the output manifest in a file use flag

    -o "./output.yaml"

## To download CI config passing criteria through manifest file
    use flag -p "./inputmanifest.yaml"
Provide metadata type as YAML_DOWNLOAD or JSON_DOWNLOAD


## To patch preCI-preCD for existing CI piplines

Update the downloaded manifest by following the steps
1. change the metadata type to PATCH
2. one entry in payload is for one ci-pipeline associated to it.
3. change the criteria as per your needs for which the particular payload will be used to path.
4. For append pre/post CI steps , introduced new flag  namely `appendPreCiSteps` and `appendPostCiSteps`.
5. update the pre-ci/post-ci steps as necessary
6. save the file and run the command  
   `
   devtctl applyCI -p ./inputmanifest.yaml
   `  
   `
   devtctl applyCI -p ./inputmanifest.json
   `   
   (both yaml/json type suported in input)

to disable interactive prompts and reply yes to all use  
`flag   --allYes  `  
to patch with empty pre or post CI stage(which will lead to deletion of current configuration) use   
`
flag --overrideEmptySteps
`

# Sample commands for getCD and patchCD

## To download CD config passing criteria through flags
    devtctl getCD --appIdsCD="1 2 3"  
    devtctl getCD --appNamesCD="app1 app2"  
    devtctl getCD --projectNamesCD="project1 project2"  
    devtctl getCD --envNamesCD="env1 env2" 

All these flags can be used in any combinations. In the sample inputManifest files, there are additional, more complex criteria that users can utilize: `includesAppNames`. These criteria use regular expressions to search for and filter apps based on their app names.

Let's explain these criteria in a more straightforward way for users to easily understand:

`includesAppNames`: This criteria allows you to specify a regular expression to filter apps based on their app names. If you define includesAppNames, it works similarly to using the --appNames flag via the command-line interface (CLI). By setting includesAppNames, you will receive all the apps whose app names match the provided regular expression.


To specify the format of output use flag

    --formatCD=json   
    --formatCD=yaml    
(yaml is default)  
To get the output manifest in a file use flag

    -o "./output.yaml"
## To download CD config passing criteria through manifest file
    use flag -p "./inputmanifest.yaml"
Provide metadata type as YAML_DOWNLOAD or JSON_DOWNLOAD

## To patch preCD-postCD for existing CD piplines

Update the downloaded manifest by following the steps
1. change the metadata type to PATCH
2. one entry in payload is for one cd-pipeline associated to it.
3. change the criteria as per your needs for which the particular payload will be used to path
4. For append pre/post CD steps , introduced new flag  namely `appendPreCdSteps` and `appendPostCdSteps`.
5. update the pre-cd/post-cd steps as necessary
6. save the file and run the command  
   `
   devtctl patchCD -p ./inputmanifest.yaml
   `  
   `
   devtctl patchCD -p ./inputmanifest.json
   `   
   (both yaml/json type suported in input)

to disable interactive prompts and reply yes to all use  
`flag   --allYesCD  `  
to patch with empty pre or post CI stage(which will lead to deletion of current configuration) use   
`
flag --overrideEmptyStepsCD
`







```
devtctl addEnv     -a <auth-token> \
                   -u <server-url> \
                   -p <path for config yaml file>
```

#### Sample config file for adding cluster
```
envPayload:
  - environment_name: "test-demo"
    namespace: "test-demo"
    cluster_id: 1
    is_production: true
  - environment_name: "test-demo1"
    namespace: "test-demo1"
    cluster_id: 1
    is_production: false
    description: "this is staging env"
```

The outcome entails the addition of two environments to the cluster with clusterId = '1'.
## Sample Commands to get Notification settings

```
devtctl get notif -a <auth-token> \
                     -u <server-url> \
```

Set offset, size via flags default set to 0,20
Set flag internalOnly to true to view only notification settings with imageScanner as its event type, default set to false
Set flag format for yaml or json

```
devtctl get notif -a <auth-token> \
                     -u <server-url> \
                     --size=20 --offset=0 --format=json --internalOnly=false
```
## Sample Command for creating notification
```
devtctl create notif -a <auth-token> \
                     -u <server-url> \
                     -p <path for config yaml or json file>
```
To avoid excluding duplicate notification and receiving only difference of existing notification set flag excludeDuplicates to false

```
 --excludeDuplicateVulnerability= false [default set to true]
```

```
devtctl create notif -a <auth-token> \
                     -u <server-url> \
                     -p <path for config yaml or json file> \
                     --excludeDuplicateVulnerability= false
```
#### Sample config file for creating notification

```
apiVersion: v1
spec:
  payload:
    - criteria:
        - webhookConfig:
            - webhookUrl: "https://example.com/webhook"
              configName: "TestWebhook"
              header:
               Content-Type: application/json
               Authorization: Bearer xyz123
               api-token: eyJhbGciOiJIUzI1NiIsInR5cCI6I…………
              payload: "{\"dockerImage\": \"{{devtronContainerImageRepo}}:{{devtronContainerImageTag}}\"}"
              description: Description for webhook configuration
            - webhookUrl: https://example2.com/webhook
              configName: TestWebhook2
              header:
                Content-Type: application/json
                Authorization: Bearer xyz1234567
                api-token: eyJhbGciOiJIUzI1NiIsInR5cCI6I…………
              payload: "{\"dockerImage\": \"{{devtronContainerImageRepo}}:{{devtronContainerImageTag}}\"}"
              description: Description for webhook configuration2
          notificationRequest:
            notificationConfigRequest:
              - appNames:
                  - test
                pipelineType: CI
                event:
                  success: true
          severity:
            critical: true
            moderate: false
            low: false
```
### Available Variables In webhookconfigs Payload:
1. **Name:** eventType

   **DataType:** string

   **Usage:** ``` "{\"notificationType\": \"{{eventType}}\"}" ```

2. **Name:** devtronAppId

   **DataType:** number

   **Usage:** ``` "{\"devtronAppId\": \"{{devtronAppId}}\"}" ```
3. **Name:** devtronEnvId

   **DataType:** number

   **Usage:** ``` "{\"devtronEnvId\": \"{{devtronEnvId}}\"}" ```
4. **Name:** devtronAppName

   **DataType:** string

   **Usage:** ``` "{\"devtronAppName\": \"{{devtronAppName}}\"}" ```
5. **Name:** devtronEnvName

   **DataType:** string

   **Usage:** ```  "{\"devtronEnvName\": \"{{devtronEnvName}}\"}" ```
6. **Name:** devtronCdPipelineId

   **DataType:** number

   **Usage:** ``` "{\"devtronCdPipelineId\": \"{{devtronCdPipelineId}}\"}" ```
7. **Name:** devtronCiPipelineId

   **DataType:** number

   **Usage:** ``` "{\"devtronCiPipelineId\": \"{{devtronCiPipelineId}}\"}" ```
8. **Name:** devtronApprovedByEmail

   **DataType:** string[]

   **Usage:**  ``` "{\"devtronApprovedByEmail\": \"{{devtronApprovedByEmail}}\"}" ```
9. **Name:** devtronTriggeredByEmail

   **DataType:** string

   **Usage:** ``` "{\"devtronTriggeredByEmail\": \"{{devtronTriggeredByEmail}}\"}" ```
10. **Name:** devtronContainerImageTag

    **DataType:** string

    **Usage:** ```  "{\"devtronContainerImageTag\": \"{{devtronContainerImageTag}}\"}" ```
11. **Name:** devtronContainerImageRepo

    **DataType:** string

    **Usage:** ``` "{\"devtronContainerImageRepo\": \"{{devtronContainerImageRepo}}\"}" ```
12. **Name:** scannedAt

    **DataType:** Date

    **Usage:** ``` "{\"scannedAt\": {{{scannedAt}}}}" ```
13. **Name:** scannedBy

    **DataType:** string

    **Usage:** ``` "{\"scannedBy\": \"{{scannedBy}}\"}" ```
14. **Name:** vulnerabilities

    **DataType:** vulnerability[]

    **Usage:**  ``` "{\"vulnerabilities\": [{{{vulnerabilities.getAll}}}]" ```
15. **Name:** severityCount

    **DataType:** severityCount

    **Usage:**  ``` "{\"severityCount\": {{{severityCount.getAll}}}" ```
16. **Name:** ciMaterials

    **DataType:** ciMaterials[]

    **Usage:**   ``` "{\"ciMaterials\": [{{{ciMaterials.getAll}}}]" ```
17. **Name:** buildHistoryLink

    **DataType:** string

    **Usage:**  ``` "{\"buildHistoryLink\": \"{{buildHistoryLink}}\"}" ```
18. **Name:** appDetailsLink

    **DataType:** string

    **Usage:**    ``` "{\"appDetailsLink\": \"{{appDetailsLink}}\"}" ```

### Description on configuring notification via create notif command

**Case - 1: Given appNames or appName, and pipeline Type CI / CD**

Notification is configured for all current and future pipelines (CI / CD) matching with the appNames or appName

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
payload:
- criteria:
    - configId:
        - 3
      notificationRequest:
          notificationConfigRequest:
            - appNames:
                - test
              pipelineType: CI
              event:
                imageScanner: true
      severity:
          critical: true
          moderate: true
          low: true

```
_If webhook is to be created_

```yaml

apiVersion: v1
spec:
payload:
- criteria:
    - webhookConfig:
        - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
          configName: "kushtest"
          header:
            Content-Type: application/json
          payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
          description: Description for webhook configuration
          notificationRequest:
             notificationConfigRequest:
                - appNames:
                   - test
                  pipelineType: CI
                  event:
                    imageScanner: true
          severity:
               critical: true
               moderate: true
               low: true

```
**Case - 2: Given projectNames or projectName and pipeline Type CI / CD**

Notification is configured for all current and future (CI / CD) pipelines matching with the projectNames or projectName

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
payload:
  - criteria:
      - configId:
          - 3
        notificationRequest:
             notificationConfigRequest:
              - teamNames:
                  - devtron-demo
                  - test
                pipelineType: CD
                event:
                    success: true
                    failure: true
                    trigger: true
```

_If webhook is to be created_

```yaml
apiVersion: v1
spec:
payload:
  - criteria:
      - webhookConfig:
          - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
            configName: "kushtest"
            header:
               Content-Type: application/json
            payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
            description: Description for webhook configuration
            notificationRequest:
              notificationConfigRequest:
                - teamNames:
                  - devtron-demo
                  - test
                  pipelineType: CD
                  event:
                    success: true
                    failure: true
                    trigger: true
```
**Case - 3: Given envNames or envName**

Notification is configured for all current and future ( CD) pipelines matching with the envName or envNames

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
payload:
  - criteria:
      - configId:
          - 4
        notificationRequest:
            notificationConfigRequest:
              - envNames:
                  - devtron-10-env-1
                pipelineType: CD
                event:
                    success: true
                    failure: true
                    trigger: true
```

_If webhook is to be created_

```yaml
apiVersion: v1
spec:
  payload:
    - criteria:
        - webhookConfig:
            - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
              configName: "kushtest"
              header:
                Content-Type: application/json
              payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
              description: Description for webhook configuration
          notificationRequest:
            notificationConfigRequest:
              - envNames:
                  - devtron-10-env-1
                pipelineType: CD
                event:
                  success: true
                  failure: true
                  trigger: true
```

**Case - 4: Given pipelineId and pipeline Type CI / CD**

Notification is configured only for the pipeline matching with the id and type

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
  payload:
    - criteria:
        - configId:
            - 3
          notificationRequest:
            notificationConfigRequest:
              - pipelineId:
                  - 12
                pipelineType: CI
                event:
                  imageScanner: true
            severity:
              critical: true
              moderate: true
              low: false
```
_If webhook is to be created_
```yaml
apiVersion: v1
spec:
  payload:
    - criteria:
        - webhookConfig:
            - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
              configName: "kushtest"
              header:
                Content-Type: application/json
              payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
              description: Description for webhook configuration
          notificationRequest:
            notificationConfigRequest:
              - pipelineId:
                  - 12
                pipelineType: CI
                event:
                  imageScanner: true
            severity:
              critical: true
              moderate: true
              low: false
```
**Case - 5: Given appNames or appName, projectNames or project Name and pipeline Type CI / CD**

Notification is configured for all current and future ( CD) pipelines matching with the appNames or appName, projectNames or projectName

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
  payload:
    - criteria:
        - configId:
            - 6
          notificationRequest:
            notificationConfigRequest:
              - appNames:
                  - test-app-1
                teamNames:
                  - devtron-demo
                pipelineType: CI
                event:
                  success: true
                  failure: true
                  trigger: true
```
_If webhook is to be created_

```yaml
apiVersion: v1
spec:
 payload:
   - criteria:
       - webhookConfig:
           - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
             configName: "kushtest"
             header:
               Content-Type: application/json
             payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
             description: Description for webhook configuration
         notificationRequest:
           notificationConfigRequest:
             - appNames:
                 - test-app-1
               teamNames:
                 - devtron-demo
               pipelineType: CI
               event:
                 success: true
                 failure: true
                 trigger: true
```
**Case - 6: Given  projectNames or projectName, envNames or envName and pipeline Type CI / CD**

Notification is configured for all current and future ( CI / CD) pipelines matching with the projectNames or projectName and another notification is configured for all current and future CD pipelines matching with envName or envNames

**Sample Payload**

_If already existing webhook is to be used for configuring notification_

```yaml
apiVersion: v1
spec:
 payload:
   - criteria:
       - configId:
           - 4
         notificationRequest:
           notificationConfigRequest:
             - teamNames:
                 - devtron-demo
               envNames:
                 - devtron-10-env-1
                 - devtron-10-env-2
               pipelineType: CI
               event:
                 success: true
                 failure: true
                 trigger: true
```
_If webhook is to be created_

```yaml
apiVersion: v1
spec:
 payload:
   - criteria:
       - webhookConfig:
           - webhookUrl: "https://webhook.site/fa3fea61-c066-4cb5-85cf-5afa4b3f092c"
             configName: "kushtest"
             header:
               Content-Type: application/json
             payload: '{"text": [{{{vulnerabilities.getAll}}}]}'
             description: Description for webhook configuration
         notificationRequest:
           notificationConfigRequest:
             - teamNames:
                 - devtron-demo
               envNames:
                 - devtron-10-env-1
                 - devtron-10-env-2
               pipelineType: CI
               event:
                 success: true
                 failure: true
                 trigger: true

```
## Sample Command for deleting notification setting
```
devtctl delete notif -a <auth-token> \
                     -u <server-url> \
                     -p <path for config yaml or json file>
```

#### Sample config file for deleting notification setting

```
apiVersion: v1
spec:
  payload:
    - criteria:
        viewIds:
          - 27
          - 28
```
## Sample Command for updating notification setting
```
devtctl update notif -a <auth-token> \
                     -u <server-url> \
                     -p <path for config yaml or json file>
```
To avoid excluding duplicate notification and receiving only difference of existing notification set flag excludeDuplicates to false

```
 --excludeDuplicateVulnerability= false [default set to true]
```

```
devtctl update notif -a <auth-token> \
                     -u <server-url> \
                     -p <path for config yaml or json file> \
                     --excludeDuplicateVulnerability= false
```
#### Sample config file for updating notification setting

```
apiVersion: v1
spec:
  payload:
    - criteria:
       - notificationUpdateRequest:
           updateType: events
           notificationConfigRequest:
             - id: 78
               event:
                 failure: true
                 success: true
               providers:
                 - dest: webhook
                   configId: 1
         severity:
           critical: true
           moderate: true
           low: true
```

## Sample Commands to get Notification channel configurations
```
devtctl get configNotifChannel -a <auth-token> \
                               -u <server-url> \
```
To specify format of result add flag format

```
 --format= yaml or json
```
```
devtctl get configNotifChannel -a <auth-token> \
                               -u <server-url> \
                               --format = yaml
```
## Sample Commands for changeDeployType and triggerDeploy

## Sample Commands for migrateChartStore and triggerChartStore
# Sample Commands for Deployment Window

Cmd: `deploymentWindow`

Description: `deploymentWindow` manages deployment windows, defining timeframes for deployments. Create, delete, update, and list deployment window profiles for precise control over deployment timing. Enforce schedules, coordinate releases, and maintain system stability by restricting deployments.

The deployment window comprises a variety of sub-commands and flags, each serving distinct functionalities and options:

### Sub-commands:
- `devtctl create dw -p [filepath]`: Creates deployment window profile
- `devtctl apply dw -p [filepath]`: Applies/updates the changes in deployment window profile
- `devtctl get dw --profileName [profileName]`: Gets the deployment window profile
- `devtctl  delete dw --profileNames [p1,p2]`: Deletes the deployment window profile 

### Flags:
- `--profileName string`: Accept only one profile name at a time, use while using `get dw`.
- `--profileNames string`: Comma-separated list of profile names, use while using `delete dw`.
- `--policyName string`: Accept only one policy name at a time, use while using `get appEnvList`.
- `-f, --outputFormat string`: Provide output format `json` or `yaml`. Defaults to `yaml`.
- `-o, --out-path string`: Provide file path where you want to store the output. It supports only in:
    - `devtctl get dw --profileName [profile name]`
    - `devtctl get appEnvList --policyName [profile name]`

### Examples:
### Create Deployment Window Profile
```bash
devtctl create dw -p ./createDeploymentWindow.yaml
```
- Creates the deployment window profile and takes YAML or JSON as an input.
- `createDeploymentWindow.yaml` contains data in YAML or JSON format.
- `-p` or `--path` is used to provide the file path.
- Also applies this policy on given apps and envs which are provided in the YAML or JSON file (this is optional).

### Apply Deployment Window Profile
```bash
devtctl apply dw -p ./createDeploymentWindow.txt
```
- Updates the deployment window profile and takes YAML or JSON as an input.
- `createDeploymentWindow.txt` contains data in YAML or JSON format.
- `-p` or `--path` is used to provide the file path.
- Also applies this policy on given apps and envs which are provided in the YAML or JSON file (this is optional).

### Delete Deployment Window Profile
```bash
devtctl delete dw --profileNames profile1,profile2
```
- Delete the deployment window profile for the given profile names.
- `--profileNames` is used to provide the profile names.

### Get Deployment Window Profile
```bash
devtctl get dw --profileName profile1 -f json -o ./file.txt
```
- Fetches the deployment window profile for the given profile name.
- `--profileName` is used to provide the profile name.
- `-f` or `--outputFormat` is used to provide output format JSON or YAML. Defaults to YAML (this is optional flag).
- `-o` or `--out-path` is used to provide file path where you want to store the output. (this is optional flag).

### Fetch List of All Deployment Window Profiles
```bash
devtctl get dw
```
- Fetches list of all deployment window profiles.

### Apply Policy
```bash
devtctl applyPolicy -p ./applypolicy.yaml
```
- Apply policy on the given appNames and envNames which are provided in the file `applypolicy.txt`.
- `applypolicy.txt` contains data in YAML or JSON format.
- `-p` or `--path` is used to provide file path.

### Sample Input Files:
- `createDeploymentWindow.txt` (yaml format):
```yaml
apiVersion: v1
kind: CD
metadata:
  type: YAML_DOWNLOAD
spec:
  deploymentWindowProfile:
    id: 0
    name: test-profile-1
    description: test-profile-1
    displayMessage: test-profile-1
    ExcludedUsersEmails: []
    isUserExcluded: false
    isSuperAdminExcluded: true
    type: BLACKOUT
    deploymentWindowList:
    - id: 0
      timeFrom: 2024-03-02T12:15:34.991Z
      hourMinuteFrom: ""
      hourMinuteTo: ""
      dayTo: 0
      timeTo: 2024-10-02T12:15:34.991Z
      dayFrom: 0
      weekdayFrom: ""
      weekdayTo: ""
      frequency: FIXED
      weekdays: []
    - id: 0
      timeFrom: 2024-04-02T12:15:34.991Z
      hourMinuteFrom: ""
      hourMinuteTo: ""
      dayTo: 0
      timeTo: 2024-11-01T12:15:34.991Z
      dayFrom: 0
      weekdayFrom: ""
      weekdayTo: ""
      frequency: FIXED
      weekdays: []
    timeZone: Asia/Kolkata
    enabled: true
  applyPolicy:
    appEnvNameList:
     - appNames:
         - "newapp-1"
         - "newapp-2"
       envNames:
         - "testsubs"
         - "testsubs1"
         - "testsubs2"
     - appNames:
         - "newapp-1"
         - "newapp-2"
       envNames:
         - "testsubs"
         - "testsubs1"
         - "testsubs2"
```
- `createDeploymentWindow.txt` (json format):
```
{
  "apiVersion": "v1",
  "kind": "CD",
  "metadata": {
      "type": "JSON_DOWNLOAD"
  },
  "spec": {
      "deploymentWindowProfile": {
          "id": 0,
          "name": "test-profile-1",
          "description": "test-profile-1",
          "displayMessage": "test-profile-1",
          "excludedUsersList": [],
          "isUserExcluded": false,
          "isSuperAdminExcluded": true,
          "type": "BLACKOUT",
          "deploymentWindowList": [
              {
                  "id": 0,
                  "timeFrom": "2024-03-02T12:15:34.991Z",
                  "hourMinuteFrom": "",
                  "hourMinuteTo": "",
                  "dayTo": 0,
                  "timeTo": "2024-10-02T12:15:34.991Z",
                  "dayFrom": 0,
                  "weekdayFrom": "",
                  "weekdayTo": "",
                  "frequency": "FIXED",
                  "weekdays": null
              },
              {
                  "id": 0,
                  "timeFrom": "2024-04-02T12:15:34.991Z",
                  "hourMinuteFrom": "",
                  "hourMinuteTo": "",
                  "dayTo": 0,
                  "timeTo": "2024-11-01T12:15:34.991Z",
                  "dayFrom": 0,
                  "weekdayFrom": "",
                  "weekdayTo": "",
                  "frequency": "FIXED",
                  "weekdays": null
              }
          ],
          "timeZone": "Asia/Kolkata",
          "enabled": true
      }
  }
}
```
- `Output` (prints in file format if no -o flag is used):
```
apiVersion: v1
kind: CD
metadata:
  type: YAML_DOWNLOAD
spec:
  deploymentWindowProfile:
    id: 117
    name: test-profile-1
    description: test-profile-1
    displayMessage: test-profile-1
    excludedUsersList: []
    isUserExcluded: false
    isSuperAdminExcluded: true
    type: BLACKOUT
    deploymentWindowList:
      id: 0
      timeFrom: 2024-03-02T12:15:34.991Z
      hourMinuteFrom: ""
      hourMinuteTo: ""
      dayTo: 0
      timeTo: 2024-10-02T12:15:34.991Z
      dayFrom: 0
      weekdayFrom: ""
      weekdayTo: ""
      frequency: FIXED
      weekdays: []
      id: 0
      timeFrom: 2024-04-02T12:15:34.991Z
      hourMinuteFrom: ""
      hourMinuteTo: ""
      dayTo: 0
      timeTo: 2024-11-01T12:15:34.991Z
      dayFrom: 0
      weekdayFrom: ""
      weekdayTo: ""
      frequency: FIXED
      weekdays: []
    timeZone: Asia/Kolkata
    enabled: true
    ```

```
# Devtron CLI for Image Promotion Policies

This is a command-line interface (CLI) tool for managing artifact promotion policies in the Devtron platform. It allows users to create, retrieve, update, apply, and delete artifact promotion policies through simple command-line commands.

## Usage

### 1. Create Image Promotion Policy

```bash
devtctl create imagePromotionPolicy \
    --name="policyName" \
    --description="desc" \
    --passCondition="true" \
    --failCondition="false" \
    --approverCount=0 \
    --allowRequestFromApprove=false \
    --allowImageBuilderFromApprove=false \
    --allowApproverFromDeploy=false \
    --applyPath="./inputFileForApplyingPolicy.yaml"
```


#### Sample yaml file for applyPolicy 

```
apiVersion: v1
kind: artifactPromotionPolicy
spec:
  payload:
    applicationEnvironments:
      - appName: "app-testing"
        envName: "env-demo"
    applyToPolicyNames: 
      - "test-policy"
    removePolicyNames:
      - "test-policy-1"
```

### 2. Get List of Promotion Policies

```bash
devtctl get listOfPromotionPolicies \
    --search="" \
    --sortBy="" \
    --sortOrder="" \
    --expand
```

### 3. Get Image Promotion Environment List

```bash
devtctl get imagePromotionEnvList \
    --appNames="AppNames" \
    --envNames="devtron-demo" \
    --policyNames="testing_subcmd1" \
    --sortBy="" \
    --sortOrder="" \
    --offset=0 \
    --size=20
```

### 4. Apply Policy

```bash
devtctl apply policy \
    -p="./sample/applyPolicy.yaml"
```

### 5. Get Artifact Promotion Policy

```bash
devtctl get artifactPromotionPolicy \
    --name="nameOfPolicy"
```

### 6. Update Artifact Promotion Policy

```bash
 devtctl update artifactPromotionPolicy \
    --policyName="policy_name" \
    --name="new_name" \
    --description="testing" \
    --pass_condition="pass" \
    --fail_condition="fail" \
    --approver_count=0 \
    --allow_request_from_approve=false \
    --allow_image_builder_from_approve=false \
    --allow_approver_from_deploy=false
```

### 7. Delete Promotion Policy

```bash
 devtctl delete artifactPromotionPolicy \
    --name="string"
```


