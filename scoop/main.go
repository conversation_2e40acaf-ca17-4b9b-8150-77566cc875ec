package main

import (
	"flag"
	"fmt"
	"github.com/devtron-labs/scoop/internal/config"
	"os"
	"path/filepath"
)

func main() {
	// Parse command line flags
	kubeconfig := filepath.Join(os.<PERSON>env("HOME"), ".kube", "config")
	flag.StringVar(&kubeconfig, "kubeconfig", kubeconfig, "kubeconfig file")
	flag.Parse()

	// Create application configuration
	appConfig := config.NewAppConfig(kubeconfig)

	// Validate configuration
	if err := appConfig.Validate(); err != nil {
		fmt.Printf("Configuration error: %v\n", err)
		os.Exit(1)
	}

	// Create and run the server
	srv := server.NewServer(appConfig, signals.SetupSignalHandler())
	if err := srv.Run(); err != nil {
		fmt.Printf("Server error: %v\n", err)
		os.Exit(1)
	}
}
