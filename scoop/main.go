package main

import (
	"flag"
	"fmt"
	"github.com/devtron-labs/scoop/internal/config"
	"os"
	"path/filepath"
)

func main() {
	// Parse command line flags
	kubeconfig := filepath.Join(os.<PERSON>env("HOME"), ".kube", "config")
	flag.StringVar(&kubeconfig, "kubeconfig", kubeconfig, "kubeconfig file")
	flag.Parse()

	// Create application configuration
	appConfig := config.NewAppConfig(kubeconfig)

	// Validate configuration
	if err := appConfig.Validate(); err != nil {
		fmt.Printf("Configuration error: %v\n", err)
		os.Exit(1)
	}

	// Initialize the application using Wire
	app, err := InitializeApp(appConfig)
	if err != nil {
		fmt.Printf("Failed to initialize app: %v\n", err)
		os.Exit(1)
	}

	// Run the application
	if err := app.Run(); err != nil {
		fmt.Printf("Server error: %v\n", err)
		os.Exit(1)
	}
}
