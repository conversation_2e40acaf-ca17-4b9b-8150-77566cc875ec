[{"Category": "DEVTRON", "Fields": [{"Env": "API_TIMEOUT_SECS", "EnvType": "int", "EnvValue": "5", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CACHED_GVKs", "EnvType": "string", "EnvValue": "[]", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CACHED_NAMESPACES", "EnvType": "", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_ATTEMPT_LIMIT", "EnvType": "int32", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_LIST_PAGE_BUFFER_SIZE", "EnvType": "int32", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_LIST_PAGE_SIZE", "EnvType": "int64", "EnvValue": "500", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_LIST_SEMAPHORE_SIZE", "EnvType": "int64", "EnvValue": "5", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_RESYNC_DURATION", "EnvType": "", "EnvValue": "12h", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_RETRY_USE_BACKOFF", "EnvType": "bool", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_CACHE_WATCH_RESYNC_DURATION", "EnvType": "", "EnvValue": "10m", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_ID", "EnvType": "int", "EnvValue": "1", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "CLUSTER_SYNC_RETRY_TIMEOUT_DURATION", "EnvType": "", "EnvValue": "10s", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST", "EnvType": "int", "EnvValue": "25", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_IDLE_CONN_TIMEOUT", "EnvType": "int", "EnvValue": "300", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_KEEPALIVE", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TCP_TIMEOUT", "EnvType": "int", "EnvValue": "30", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "K8s_TLS_HANDSHAKE_TIMEOUT", "EnvType": "int", "EnvValue": "10", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "LOG_LEVEL", "EnvType": "int", "EnvValue": "0", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "ORCHESTRATOR_URL", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_EXPORT_PROM_METRICS", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_FAILURE_QUERIES", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_ALL_QUERY", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_LOG_SLOW_QUERY", "EnvType": "bool", "EnvValue": "true", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "PG_QUERY_DUR_THRESHOLD", "EnvType": "int64", "EnvValue": "5000", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "RUNTIME_CONFIG_LOCAL_DEV", "EnvType": "LocalDevMode", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "TOKEN", "EnvType": "string", "EnvValue": "", "EnvDescription": "", "Example": "", "Deprecated": "false"}, {"Env": "USE_CUSTOM_HTTP_TRANSPORT", "EnvType": "bool", "EnvValue": "false", "EnvDescription": "", "Example": "", "Deprecated": "false"}]}]