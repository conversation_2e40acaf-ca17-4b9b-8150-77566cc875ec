package router

import (
	"net/http"

	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/internal/helpers"
	"github.com/devtron-labs/scoop/internal/middleware"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	"github.com/devtron-labs/scoop/pkg/namespaces"
	"github.com/devtron-labs/scoop/pkg/watcher"
	"github.com/devtron-labs/scoop/types"
)

// Router handles HTTP route registration
type Router struct {
	passKey              string
	pprofHandler         *api.PprofHandler
	resourceHandler      *api.ResourceHandler
	eventWatcherService  *watcher.EventWatcherService
}

// NewRouter creates a new router instance
func NewRouter(passKey string, pprofHandler *api.PprofHandler, resourceHandler *api.ResourceHandler) *Router {
	return &Router{
		passKey:         passKey,
		pprofHandler:    pprof<PERSON><PERSON><PERSON>,
		resourceHandler: resourceHandler,
	}
}

// GetEventWatcherService returns the event watcher service for cleanup
func (r *Router) GetEventWatcherService() *watcher.EventWatcherService {
	return r.eventWatcherService
}

// RegisterRoutes registers all HTTP routes
func (r *Router) RegisterRoutes() {
	// Register pod and node routes
	r.registerPodRoutes()
	r.registerNodeRoutes()
	
	// Register pprof routes
	r.registerPprofRoutes()
	
	// Register resource routes
	r.registerResourceRoutes()
	
	// Register watcher routes if configured
	r.registerWatcherRoutes()
}

// registerPodRoutes registers pod-related routes
func (r *Router) registerPodRoutes() {
	http.HandleFunc(config.PodInfoEndpoint, middleware.AuthMiddleware(r.passKey, helpers.GetPodInfo))
	http.HandleFunc(config.PodListEndpoint, middleware.AuthMiddleware(r.passKey, helpers.GetPodList))
}

// registerNodeRoutes registers node-related routes
func (r *Router) registerNodeRoutes() {
	http.HandleFunc(config.NodeListEndpoint, middleware.AuthMiddleware(r.passKey, helpers.GetNodeList))
}

// registerPprofRoutes registers pprof routes
func (r *Router) registerPprofRoutes() {
	for path, handler := range r.pprofHandler.RegisterRoutes() {
		http.HandleFunc(path, handler)
	}
}

// registerResourceRoutes registers resource-related routes
func (r *Router) registerResourceRoutes() {
	http.HandleFunc(config.ApiResourcesEndpoint, middleware.AuthMiddleware(r.passKey, r.resourceHandler.GetApiResource))
	http.HandleFunc(config.ResourceListEndpoint, middleware.AuthMiddleware(r.passKey, r.resourceHandler.GetResourceList))
	http.HandleFunc(config.ResourceTreeEndpoint, middleware.AuthMiddleware(r.passKey, r.resourceHandler.GetResourceTreeForNodes))
	http.HandleFunc(config.K8sCacheConfigEndpoint, middleware.AuthMiddleware(r.passKey, r.resourceHandler.UpdateResourceCacheConfig))
}

// registerWatcherRoutes registers watcher routes if watcher is configured
func (r *Router) registerWatcherRoutes() {
	watcherEnvConfig := types.NewWatcherConfig()
	if !watcherEnvConfig.IsValid() {
		return
	}

	namespaceStore := namespaces.NewNamespaceStore()
	watcherHandler := r.setupWatcher(namespaceStore)

	http.HandleFunc(config.WatcherCUDEndpoint, middleware.AuthMiddleware(r.passKey, watcherHandler.HandleWatchCUD))
	http.HandleFunc(config.WatcherGetEndpoint, middleware.AuthMiddleware(r.passKey, watcherHandler.GetWatcher))
	http.HandleFunc(config.NamespaceCUDEndpoint, middleware.AuthMiddleware(r.passKey, watcherHandler.HandleNamespaceCUD))
}

// setupWatcher initializes the watcher service and returns a handler
func (r *Router) setupWatcher(namespaceStore namespaces.NamespaceStore) *api.WatcherHandler {
	// Get the watcher config
	watcherEnvConfig := types.NewWatcherConfig()

	// Create the event watcher service
	onResourceUpdateHandlers := make([]cache.OnResourceUpdatedHandler, 0)
	r.eventWatcherService = watcher.NewEventWatcher(watcherEnvConfig, namespaceStore)

	// Add the event watcher service to the resource update handlers
	onResourceUpdateHandlers = append(onResourceUpdateHandlers, r.eventWatcherService.OnResourceUpdate)

	// Start watching for events
	go r.eventWatcherService.Watch()

	// Create and return the watcher handler
	return api.NewWatcherHandler(r.passKey, namespaceStore, r.eventWatcherService)
}
