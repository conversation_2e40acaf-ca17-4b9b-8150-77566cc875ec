package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// AppConfig holds the application configuration
type AppConfig struct {
	KubeConfig     string
	PassKey        string
	Retention      int
	ResyncDuration time.Duration
	Port           string
}

// NewAppConfig creates a new application configuration
func NewAppConfig(kubeConfig string) *AppConfig {
	config := &AppConfig{
		KubeConfig:     kubeConfig,
		PassKey:        "abc", // Default value
		Retention:      0,     // Default value
		ResyncDuration: 5 * time.Minute,
		Port:           DefaultPort,
	}

	// Load from environment variables
	if authKey, found := os.LookupEnv("PASS_KEY"); found {
		config.PassKey = authKey
	}

	if len(config.PassKey) == 0 {
		fmt.Println("Missing passkey, set env variable PASS_KEY")
		os.Exit(1)
	}

	if r, found := os.LookupEnv("RETENTION"); found {
		ri, err := strconv.Atoi(r)
		if err == nil {
			config.Retention = ri
		}
	}

	if port, found := os.LookupEnv("PORT"); found {
		config.Port = ":" + port
	}

	return config
}

// Validate checks if the configuration is valid
func (c *AppConfig) Validate() error {
	if c.KubeConfig == "" {
		return fmt.Errorf("kubeconfig is required")
	}
	if c.PassKey == "" {
		return fmt.Errorf("passkey is required")
	}
	return nil
}
