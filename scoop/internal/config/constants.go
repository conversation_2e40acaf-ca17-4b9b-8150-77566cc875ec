package config

// HTTP related constants
const (
	ContentType     = "Content-Type"
	ContentLength   = "Content-Length"
	ApplicationJSON = "application/json"
	PassKeyHeader   = "X-PASS-KEY"
)

// API endpoint constants
const (
	PodInfoEndpoint        = "/k8s/pod"
	PodListEndpoint        = "/k8s/pod/all"
	NodeListEndpoint       = "/k8s/node/all"
	WatcherCUDEndpoint     = "/k8s/watcher"
	WatcherGetEndpoint     = "/watcher"
	ApiResourcesEndpoint   = "/k8s/api-resources"
	ResourceListEndpoint   = "/k8s/resources"
	ResourceTreeEndpoint   = "/k8s/resource-tree"
	K8sCacheConfigEndpoint = "/k8s/cache/config"
	NamespaceCUDEndpoint   = "/k8s/namespace"
)

// Debug endpoints
const (
	PprofIndexEndpoint        = "debug/pprof/index"
	PprofCmdlineEndpoint      = "debug/pprof/cmdline"
	PprofProfileEndpoint      = "debug/pprof/profile"
	PprofSymbolEndpoint       = "debug/pprof/symbol"
	PprofTraceEndpoint        = "debug/pprof/trace"
	PprofGoroutineEndpoint    = "debug/pprof/goroutine"
	PprofThreadcreateEndpoint = "debug/pprof/threadcreate"
	PprofHeapEndpoint         = "debug/pprof/heap"
	PprofBlockEndpoint        = "debug/pprof/block"
	PprofMutexEndpoint        = "debug/pprof/mutex"
	PprofAllocsEndpoint       = "debug/pprof/allocs"
)

// Default values
const (
	DefaultNamespace = "default"
	DefaultPort      = ":8080"
)
