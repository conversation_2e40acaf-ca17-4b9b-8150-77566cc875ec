package middleware

import (
	"github.com/devtron-labs/scoop/internal/config"
	"net/http"
)

// AuthMiddleware creates a middleware for authentication
func AuthMiddleware(passKey string, next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		headerPassKey := r.Header.Get(config.PassKeyHeader)
		if headerPassKey != passKey {
			WriteResponse(w, http.StatusForbidden, nil, "unauthorized")
			return
		}
		next(w, r)
	}
}
