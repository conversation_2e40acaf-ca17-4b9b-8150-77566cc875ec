package middleware

import (
	"encoding/json"
	"github.com/devtron-labs/scoop/internal/config"
	"go.uber.org/zap"
	"net/http"
)

// Response represents a standardized API response
type Response struct {
	Code   int         `json:"code,omitempty"`
	Status string      `json:"status,omitempty"`
	Result interface{} `json:"result,omitempty"`
	Error  string      `json:"errors,omitempty"`
}

// WriteResponse writes a standardized response to the HTTP response writer
func WriteResponse(w http.ResponseWriter, statusCode int, result interface{}, err string) {
	WriteResponseWithLogger(w, statusCode, result, err, nil)
}

// WriteResponseWithLogger writes a standardized response to the HTTP response writer with error logging
func WriteResponseWithLogger(w http.ResponseWriter, statusCode int, result interface{}, err string, logger *zap.SugaredLogger) {
	response := Response{
		Code:   statusCode,
		Status: http.StatusText(statusCode),
		Result: result,
		Error:  err,
	}

	responseJSON, marshalErr := json.Marshal(response)
	if marshalErr != nil {
		if logger != nil {
			logger.Errorw("failed to marshal response to J<PERSON><PERSON>", "error", marshalErr, "statusCode", statusCode)
		}
		// Fallback to a simple error response
		w.Header().Set(config.ContentType, config.ApplicationJSON)
		w.WriteHeader(http.StatusInternalServerError)
		fallbackResponse := `{"code":500,"status":"Internal Server Error","errors":"failed to marshal response"}`
		if _, writeErr := w.Write([]byte(fallbackResponse)); writeErr != nil && logger != nil {
			logger.Errorw("failed to write fallback response", "error", writeErr)
		}
		return
	}

	w.Header().Set(config.ContentType, config.ApplicationJSON)
	w.WriteHeader(statusCode)
	if _, writeErr := w.Write(responseJSON); writeErr != nil && logger != nil {
		logger.Errorw("failed to write response", "error", writeErr, "statusCode", statusCode)
	}
}
