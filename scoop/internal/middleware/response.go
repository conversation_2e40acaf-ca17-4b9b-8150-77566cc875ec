package middleware

import (
	"encoding/json"
	"github.com/devtron-labs/scoop/internal/config"
	"net/http"
)

// Response represents a standardized API response
type Response struct {
	Code   int         `json:"code,omitempty"`
	Status string      `json:"status,omitempty"`
	Result interface{} `json:"result,omitempty"`
	Error  string      `json:"errors,omitempty"`
}

// WriteResponse writes a standardized response to the HTTP response writer
func WriteResponse(w http.ResponseWriter, statusCode int, result interface{}, err string) {
	response := Response{
		Code:   statusCode,
		Status: http.StatusText(statusCode),
		Result: result,
		Error:  err,
	}
	responseJSON, _ := json.Marshal(response)
	w.<PERSON><PERSON>().<PERSON>(config.ContentType, config.ApplicationJSON)
	w.WriteHeader(statusCode)
	w.Write(responseJSON)
}
