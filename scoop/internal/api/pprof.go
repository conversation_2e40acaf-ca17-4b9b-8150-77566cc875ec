package api

import (
	"github.com/devtron-labs/scoop/pkg/pprof"
	"net/http"
)

// PprofHandler handles pprof-related API requests
type PprofHandler struct {
	Handler pprof.PProfRestHandler
}

// NewPprofHandler creates a new pprof handler
func NewPprofHandler() *PprofHandler {
	return &PprofHandler{
		Handler: pprof.NewPProfRestHandler(),
	}
}

// RegisterRoutes registers all pprof routes
func (h *PprofHandler) RegisterRoutes() map[string]http.HandlerFunc {
	return map[string]http.HandlerFunc{
		"debug/pprof/index":        h.Handler.Index,
		"debug/pprof/cmdline":      h.Handler.Cmdline,
		"debug/pprof/profile":      h.Handler.Profile,
		"debug/pprof/symbol":       h.Handler.Symbol,
		"debug/pprof/trace":        h.<PERSON>ler.Trace,
		"debug/pprof/goroutine":    h.<PERSON><PERSON>.Goroutine,
		"debug/pprof/threadcreate": h.<PERSON><PERSON>.Threadcreate,
		"debug/pprof/heap":         h.<PERSON>ler.Heap,
		"debug/pprof/block":        h.Handler.Block,
		"debug/pprof/mutex":        h.Handler.Mutex,
		"debug/pprof/allocs":       h.Handler.Allocs,
	}
}
