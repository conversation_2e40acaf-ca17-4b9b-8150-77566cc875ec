package api

import (
	"github.com/devtron-labs/scoop/pkg/namespaces"
	"github.com/devtron-labs/scoop/pkg/watcher"
	"net/http"
)

// WatcherHandler handles watcher-related API requests
type WatcherHandler struct {
	EventWatcherHandler *watcher.EventWatcherRestHandler
	NamespaceHandler    *namespaces.NamespaceRestHandler
}

// NewWatcherHandler creates a new watcher handler
func NewWatcherHandler(passKey string, namespaceStore namespaces.NamespaceStore, eventWatcherService *watcher.EventWatcherService) *WatcherHandler {
	return &WatcherHandler{
		EventWatcherHandler: watcher.NewEventWatcherRestHandler(passKey, eventWatcherService),
		NamespaceHandler:    namespaces.NewNamespaceRestHandler(passKey, namespaceStore),
	}
}

// HandleWatchCUD handles create, update, delete operations for watchers
func (h *WatcherHandler) HandleWatchCUD(w http.ResponseWriter, r *http.Request) {
	h.EventWatcherHandler.HandleWatchCUD(w, r)
}

// GetWatcher handles requests to get watcher information
func (h *WatcherHandler) GetWatcher(w http.ResponseWriter, r *http.Request) {
	h.EventWatcherHandler.GetWatcher(w, r)
}

// HandleNamespaceCUD handles create, update, delete operations for namespaces
func (h *WatcherHandler) HandleNamespaceCUD(w http.ResponseWriter, r *http.Request) {
	h.NamespaceHandler.HandleNamespaceCUD(w, r)
}
