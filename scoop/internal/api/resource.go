package api

import (
	"encoding/json"
	"github.com/devtron-labs/scoop/internal/middleware"
	cluster "github.com/devtron-labs/scoop/pkg/clusterResources"
	"github.com/devtron-labs/scoop/types"
	"go.uber.org/zap"
	"net/http"
)

// ResourceHandler handles resource-related API requests
type ResourceHandler struct {
	Logger                 *zap.SugaredLogger
	ClusterResourceManager cluster.ClusterResourceManager
}

// NewResourceHandler creates a new resource handler
func NewResourceHandler(logger *zap.SugaredLogger, resourceManager cluster.ClusterResourceManager) *ResourceHandler {
	return &ResourceHandler{
		Logger:                 logger,
		ClusterResourceManager: resourceManager,
	}
}

// GetApiResource handles requests for API resources
func (h *ResourceHandler) GetApiResource(w http.ResponseWriter, r *http.Request) {
	apiResources, err := h.ClusterResourceManager.GetApiResources(r.Context())
	h.writeResponse(w, err, apiResources)
}

// GetResourceList handles requests for resource lists
func (h *ResourceHandler) GetResourceList(w http.ResponseWriter, r *http.Request) {
	k8sRequestBean := &types.K8sRequestBean{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&k8sRequestBean)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	response, err := h.ClusterResourceManager.GetResourceList(r.Context(), k8sRequestBean)
	h.writeResponse(w, err, response)
}

// UpdateResourceCacheConfig handles requests to update resource cache configuration
func (h *ResourceHandler) UpdateResourceCacheConfig(w http.ResponseWriter, r *http.Request) {
	config := &types.Config{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&config)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	err = h.ClusterResourceManager.UpdateK8sCacheConfig(config)
	h.writeResponse(w, err, nil)
}

// GetResourceTreeForNodes handles requests for resource trees for nodes
func (h *ResourceHandler) GetResourceTreeForNodes(w http.ResponseWriter, r *http.Request) {
	req := &types.ResourceTreeForNodesRequest{}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(&req)
	if err != nil {
		w.WriteHeader(http.StatusBadRequest)
		return
	}
	response, err := h.ClusterResourceManager.GetResourceTreeForNodes(r.Context(), req)
	h.writeResponse(w, err, response)
}

// writeResponse writes a response with error handling
func (h *ResourceHandler) writeResponse(w http.ResponseWriter, err error, response any) {
	errMsg := ""
	jsonResponse := ""
	if err != nil {
		h.Logger.Errorw("error occurred while fetching data", "err", err)
		errMsg = "failed to fetch data"
	}
	if response != nil {
		jsonResponseBytes, err := json.Marshal(response)
		if err != nil {
			h.Logger.Errorw("error occurred while marshalling response", "err", err)
			errMsg = "failed to marshal response"
		}
		jsonResponse = string(jsonResponseBytes)
	}

	if len(errMsg) > 0 {
		middleware.WriteResponse(w, http.StatusInternalServerError, nil, errMsg)
	} else {
		var result interface{}
		if len(jsonResponse) > 0 {
			json.Unmarshal([]byte(jsonResponse), &result)
		}
		middleware.WriteResponse(w, http.StatusOK, result, "")
	}
}
