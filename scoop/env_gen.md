

## DEVTRON Related Environment Variables
| Key   | Type     | Default Value     | Description       | Example       | Deprecated       |
|-------|----------|-------------------|-------------------|-----------------------|------------------|
 | API_TIMEOUT_SECS | int |5 |  |  | false |
 | CACHED_GVKs | string |[] |  |  | false |
 | CACHED_NAMESPACES |  | |  |  | false |
 | CLUSTER_CACHE_ATTEMPT_LIMIT | int32 |1 |  |  | false |
 | CLUSTER_CACHE_LIST_PAGE_BUFFER_SIZE | int32 |10 |  |  | false |
 | CLUSTER_CACHE_LIST_PAGE_SIZE | int64 |500 |  |  | false |
 | CLUSTER_CACHE_LIST_SEMAPHORE_SIZE | int64 |5 |  |  | false |
 | CLUSTER_CACHE_RESYNC_DURATION |  |12h |  |  | false |
 | CLUSTER_CACHE_RETRY_USE_BACKOFF | bool | |  |  | false |
 | CLUSTER_CACHE_WATCH_RESYNC_DURATION |  |10m |  |  | false |
 | CLUSTER_ID | int |1 |  |  | false |
 | CLUSTER_SYNC_RETRY_TIMEOUT_DURATION |  |10s |  |  | false |
 | K8s_CLIENT_MAX_IDLE_CONNS_PER_HOST | int |25 |  |  | false |
 | K8s_TCP_IDLE_CONN_TIMEOUT | int |300 |  |  | false |
 | K8s_TCP_KEEPALIVE | int |30 |  |  | false |
 | K8s_TCP_TIMEOUT | int |30 |  |  | false |
 | K8s_TLS_HANDSHAKE_TIMEOUT | int |10 |  |  | false |
 | LOG_LEVEL | int |0 |  |  | false |
 | ORCHESTRATOR_URL | string | |  |  | false |
 | PG_EXPORT_PROM_METRICS | bool |true |  |  | false |
 | PG_LOG_ALL_FAILURE_QUERIES | bool |true |  |  | false |
 | PG_LOG_ALL_QUERY | bool |false |  |  | false |
 | PG_LOG_SLOW_QUERY | bool |true |  |  | false |
 | PG_QUERY_DUR_THRESHOLD | int64 |5000 |  |  | false |
 | RUNTIME_CONFIG_LOCAL_DEV | LocalDevMode |false |  |  | false |
 | TOKEN | string | |  |  | false |
 | USE_CUSTOM_HTTP_TRANSPORT | bool |false |  |  | false |

