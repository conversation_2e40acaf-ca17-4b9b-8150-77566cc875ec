all: fetch-all-env build

TARGET_BRANCH?=main

# Install wire if not present: go install github.com/google/wire/cmd/wire@latest
dep-update-oss:
	go mod edit -replace=github.com/devtron-labs/common-lib=github.com/devtron-labs/devtron-services/common-lib@$(TARGET_BRANCH)
	go mod tidy
	go mod vendor


dep-update-ent:
	go mod edit --replace=github.com/devtron-labs/common-lib-private=github.com/devtron-labs/devtron-services-enterprise/common-lib@$(TARGET_BRANCH)
	go mod tidy
	go mod vendor

build: clean wire
	go build -o main .

wire:
	@which wire > /dev/null || (echo "Installing wire..." && go install github.com/google/wire/cmd/wire@latest)
	@which wire > /dev/null && wire || $(HOME)/go/bin/wire

clean:
	rm -rf main

fetch-all-env:
	go run fetchAllEnv/fetchAllEnv.go
