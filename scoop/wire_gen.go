// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/devtron-labs/common-lib/utils"
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	"github.com/devtron-labs/scoop/pkg/clusterResources"
	"github.com/devtron-labs/scoop/pkg/singals"
	"go.uber.org/zap"
)

// Injectors from Wire.go:

// InitializeApp initializes the application with all dependencies
func InitializeApp(cfg *config.AppConfig) (*App, error) {
	sugaredLogger, err := ProvideLogger()
	if err != nil {
		return nil, err
	}
	clusterResourceManager, err := ProvideResourceManager(cfg)
	if err != nil {
		return nil, err
	}
	pprofHandler := ProvidePprofHandler()
	resourceHandler := ProvideResourceHandler(sugaredLogger, clusterResourceManager)
	v := ProvideStopChannel()
	app := NewApp(cfg, sugaredLogger, clusterResourceManager, pprofHandler, resourceHandler, v)
	return app, nil
}

// Wire.go:

// ProvideLogger provides a logger instance
func ProvideLogger() (*zap.SugaredLogger, error) {
	return utils.NewSugardLogger()
}

// ProvideResourceManager provides a cluster resource manager
func ProvideResourceManager(cfg *config.AppConfig) (cluster.ClusterResourceManager, error) {
	onResourceUpdateHandlers := make([]cache.OnResourceUpdatedHandler, 0)
	return cluster.NewClusterResourceManagerImpl(cfg.KubeConfig, onResourceUpdateHandlers)
}

// ProvidePprofHandler provides a pprof handler
func ProvidePprofHandler() *api.PprofHandler {
	return api.NewPprofHandler()
}

// ProvideResourceHandler provides a resource handler
func ProvideResourceHandler(logger *zap.SugaredLogger, resourceManager cluster.ClusterResourceManager) *api.ResourceHandler {
	return api.NewResourceHandler(logger, resourceManager)
}

// ProvideStopChannel provides a stop channel for graceful shutdown
func ProvideStopChannel() <-chan struct{} {
	return signals.SetupSignalHandler()
}
