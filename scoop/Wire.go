//go:build wireinject
// +build wireinject

package main

import (
	"github.com/devtron-labs/common-lib/utils"
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	cluster "github.com/devtron-labs/scoop/pkg/clusterResources"
	signals "github.com/devtron-labs/scoop/pkg/singals"
	"github.com/google/wire"
	"go.uber.org/zap"
)

// ProvideLogger provides a logger instance
func ProvideLogger() (*zap.SugaredLogger, error) {
	return utils.NewSugardLogger()
}

// ProvideResourceManager provides a cluster resource manager
func ProvideResourceManager(cfg *config.AppConfig) (cluster.ClusterResourceManager, error) {
	onResourceUpdateHandlers := make([]cache.OnResourceUpdatedHandler, 0)
	return cluster.NewClusterResourceManagerImpl(cfg.KubeConfig, onResourceUpdateHandlers)
}

// ProvidePprofHandler provides a pprof handler
func ProvidePprofHandler() *api.PprofHandler {
	return api.NewPprofHandler()
}

// ProvideResourceHandler provides a resource handler
func ProvideResourceHandler(logger *zap.SugaredLogger, resourceManager cluster.ClusterResourceManager) *api.ResourceHandler {
	return api.NewResourceHandler(logger, resourceManager)
}

// ProvideStopChannel provides a stop channel for graceful shutdown
func ProvideStopChannel() <-chan struct{} {
	return signals.SetupSignalHandler()
}

// InitializeApp initializes the application with all dependencies
func InitializeApp(cfg *config.AppConfig) (*App, error) {
	wire.Build(
		NewApp,
		ProvideLogger,
		ProvideResourceManager,
		ProvidePprofHandler,
		ProvideResourceHandler,
		ProvideStopChannel,
	)
	return &App{}, nil
}
