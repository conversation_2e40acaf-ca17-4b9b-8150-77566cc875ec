package main

import (
	"fmt"
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/internal/helpers"
	"github.com/devtron-labs/scoop/internal/middleware"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	cluster "github.com/devtron-labs/scoop/pkg/clusterResources"
	"github.com/devtron-labs/scoop/pkg/kubectl"
	"github.com/devtron-labs/scoop/pkg/namespaces"
	"github.com/devtron-labs/scoop/pkg/watcher"
	"github.com/devtron-labs/scoop/types"
	"go.uber.org/zap"
	"log"
	"net/http"
)

// App represents the main application with all dependencies
type App struct {
	Config          *config.AppConfig
	Logger          *zap.SugaredLogger
	ResourceManager cluster.ClusterResourceManager
	PprofHandler    *api.PprofHandler
	ResourceHandler *api.ResourceHandler
	Stop            <-chan struct{}
}

// Global variable to store the event watcher service for stopping it later
var eventWatcherServiceInstance *watcher.EventWatcherService

// NewApp creates a new App instance with all dependencies injected
func NewApp(
	cfg *config.AppConfig,
	logger *zap.SugaredLogger,
	resourceManager cluster.ClusterResourceManager,
	pprofHandler *api.PprofHandler,
	resourceHandler *api.ResourceHandler,
	stop <-chan struct{},
) *App {
	return &App{
		Config:          cfg,
		Logger:          logger,
		ResourceManager: resourceManager,
		PprofHandler:    pprofHandler,
		ResourceHandler: resourceHandler,
		Stop:            stop,
	}
}

// Run starts the server with all injected dependencies
func (app *App) Run() error {
	errChan := make(chan error)

	// Register event handlers
	kubectl.RegisterEventHandler(kubectl.PodHandler.Kind, kubectl.PodHandler)
	kubectl.RegisterEventHandler(kubectl.NodeHandler.Kind, kubectl.NodeHandler)

	// Initialize the watcher
	watcher := kubectl.NewKubeWatch(app.Config.KubeConfig, app.Config.ResyncDuration, app.Config.Retention, errChan)
	watcher.AddWatch()

	// Initialize handlers
	onResourceUpdateHandlers := make([]cache.OnResourceUpdatedHandler, 0)
	watcherEnvConfig := types.NewWatcherConfig()

	// Setup routes using helper functions
	http.HandleFunc(config.PodInfoEndpoint, middleware.AuthMiddleware(app.Config.PassKey, helpers.GetPodInfo))
	http.HandleFunc(config.PodListEndpoint, middleware.AuthMiddleware(app.Config.PassKey, helpers.GetPodList))
	http.HandleFunc(config.NodeListEndpoint, middleware.AuthMiddleware(app.Config.PassKey, helpers.GetNodeList))

	// Register pprof routes
	for path, handler := range app.PprofHandler.RegisterRoutes() {
		http.HandleFunc(path, handler)
	}

	// Setup watcher if configured
	if watcherEnvConfig.IsValid() {
		namespaceStore := namespaces.NewNamespaceStore()

		// Import the watcher package dynamically to avoid import cycles
		watcherHandler := setupWatcher(app.Config.PassKey, namespaceStore, onResourceUpdateHandlers)

		http.HandleFunc(config.WatcherCUDEndpoint, middleware.AuthMiddleware(app.Config.PassKey, watcherHandler.HandleWatchCUD))
		http.HandleFunc(config.WatcherGetEndpoint, middleware.AuthMiddleware(app.Config.PassKey, watcherHandler.GetWatcher))
		http.HandleFunc(config.NamespaceCUDEndpoint, middleware.AuthMiddleware(app.Config.PassKey, watcherHandler.HandleNamespaceCUD))
	}

	// Setup resource routes
	http.HandleFunc(config.ApiResourcesEndpoint, middleware.AuthMiddleware(app.Config.PassKey, app.ResourceHandler.GetApiResource))
	http.HandleFunc(config.ResourceListEndpoint, middleware.AuthMiddleware(app.Config.PassKey, app.ResourceHandler.GetResourceList))
	http.HandleFunc(config.ResourceTreeEndpoint, middleware.AuthMiddleware(app.Config.PassKey, app.ResourceHandler.GetResourceTreeForNodes))
	http.HandleFunc(config.K8sCacheConfigEndpoint, middleware.AuthMiddleware(app.Config.PassKey, app.ResourceHandler.UpdateResourceCacheConfig))

	// Start the server
	go func() {
		fmt.Println("starting server on", app.Config.Port)
		err := http.ListenAndServe(app.Config.Port, nil)
		log.Fatal(err)
	}()

	// Wait for shutdown signal
	select {
	case <-app.Stop:
		// We are done
		watcher.Stop()
		// Stop the event watcher if it was started
		stopEventWatcher()
		return nil
	case err := <-errChan:
		// Error starting or running a runnable
		return err
	}
}

// setupWatcher initializes the watcher service and returns a handler
func setupWatcher(passKey string, namespaceStore namespaces.NamespaceStore, handlers []cache.OnResourceUpdatedHandler) *api.WatcherHandler {
	// Get the watcher config
	watcherEnvConfig := types.NewWatcherConfig()

	// Create the event watcher service
	eventWatcherServiceInstance = watcher.NewEventWatcher(watcherEnvConfig, namespaceStore)

	// Add the event watcher service to the resource update handlers
	handlers = append(handlers, eventWatcherServiceInstance.OnResourceUpdate)

	// Start watching for events
	go eventWatcherServiceInstance.Watch()

	// Create and return the watcher handler
	return api.NewWatcherHandler(passKey, namespaceStore, eventWatcherServiceInstance)
}

// stopEventWatcher stops the event watcher service if it was started
func stopEventWatcher() {
	if eventWatcherServiceInstance != nil {
		eventWatcherServiceInstance.Stop()
		eventWatcherServiceInstance = nil
	}
}
