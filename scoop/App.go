package main

import (
	"fmt"
	"github.com/devtron-labs/scoop/internal/api"
	"github.com/devtron-labs/scoop/internal/config"
	"github.com/devtron-labs/scoop/internal/router"
	"github.com/devtron-labs/scoop/pkg/clusterCache/cache"
	cluster "github.com/devtron-labs/scoop/pkg/clusterResources"
	"github.com/devtron-labs/scoop/pkg/kubectl"
	"github.com/devtron-labs/scoop/pkg/watcher"
	"github.com/devtron-labs/scoop/types"
	"go.uber.org/zap"
	"log"
	"net/http"
)

// App represents the main application with all dependencies
type App struct {
	Config          *config.AppConfig
	Logger          *zap.SugaredLogger
	ResourceManager cluster.ClusterResourceManager
	PprofHandler    *api.PprofHandler
	ResourceHandler *api.ResourceHandler
	Stop            <-chan struct{}
	Router          *router.Router
}

// NewApp creates a new App instance with all dependencies injected
func NewApp(
	cfg *config.AppConfig,
	logger *zap.SugaredLogger,
	resourceManager cluster.ClusterResourceManager,
	pprofHandler *api.PprofHandler,
	resourceHandler *api.ResourceHandler,
	stop <-chan struct{},
) *App {
	// Create router
	appRouter := router.NewRouter(cfg.PassKey, pprofHandler, resourceHandler)

	return &App{
		Config:          cfg,
		Logger:          logger,
		ResourceManager: resourceManager,
		PprofHandler:    pprofHandler,
		ResourceHandler: resourceHandler,
		Stop:            stop,
		Router:          appRouter,
	}
}

// Run starts the server with all injected dependencies
func (app *App) Run() error {
	errChan := make(chan error)

	// Register event handlers
	kubectl.RegisterEventHandler(kubectl.PodHandler.Kind, kubectl.PodHandler)
	kubectl.RegisterEventHandler(kubectl.NodeHandler.Kind, kubectl.NodeHandler)

	// Initialize the watcher
	watcher := kubectl.NewKubeWatch(app.Config.KubeConfig, app.Config.ResyncDuration, app.Config.Retention, errChan)
	watcher.AddWatch()

	// Setup routes using the router package
	app.Router.RegisterRoutes()

	// Start the server
	go func() {
		fmt.Println("starting server on", app.Config.Port)
		err := http.ListenAndServe(app.Config.Port, nil)
		log.Fatal(err)
	}()

	// Wait for shutdown signal
	select {
	case <-app.Stop:
		// We are done
		watcher.Stop()
		// Stop the event watcher if it was started
		if eventWatcherService := app.Router.GetEventWatcherService(); eventWatcherService != nil {
			eventWatcherService.Stop()
		}
		return nil
	case err := <-errChan:
		// Error starting or running a runnable
		return err
	}
}



// stopEventWatcher stops the event watcher service if it was started
func stopEventWatcher() {
	if eventWatcherServiceInstance != nil {
		eventWatcherServiceInstance.Stop()
		eventWatcherServiceInstance = nil
	}
}
